import { appleAuth } from '@invertase/react-native-apple-authentication';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import AppleIcon from '../assets/images/apple-logo-svgrepo-com.svg';
import EmailIcon from '../assets/images/envelope-solid.svg';
import EyeSlashIcon from '../assets/images/eye-slash-solid.svg';
import EyeIcon from '../assets/images/eye-solid.svg';
import GoogleIcon from '../assets/images/icons8-google (1).svg';
import LockIcon from '../assets/images/lock-solid.svg';
import {
  FieldValidation,
  ValidateField,
  ValidateForm,
} from '../common/Validation';
import Center from '../components/Center';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

type SignedInScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'SignedIn'
>;

type Props = {
  navigation: SignedInScreenNavigationProp;
};

const LoginScreen: React.FC<Props> = ({navigation}) => {
  const {themeStyles, theme} = useTheme();
  const {
    initializing,
    signin,
    signInWithGoogle,
    signInWithMicrosoft,
    handleAppleLogin,
  } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSocialLoginLoading, setIsSocialLoginLoading] = useState(false);
  const [error, setError] = useState();
  const windowWidth = Dimensions.get('window').width;
  const iconSize = windowWidth * 0.4;
  const inputIconSize = 24;
  const [form, setForm] = useState({email: '', password: ''});
  const [validation, setValidation] = useState({email: '', password: ''});
  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [revealPassword, setRevealPassword] = useState<boolean>(false);
  ('');

  const validationSchema: FieldValidation[] = [
    {
      name: 'email',
      requirements: ['Required', 'ValidEmail'],
      displayName: 'Email',
    },
    {
      name: 'password',
      requirements: ['Required'],
      displayName: 'Password',
    },
  ];

  const onValueChange = (name: string, value: any) => {
    let newForm = {...form, [name]: value};
    setForm(newForm);
    setIsFormValid(ValidateForm(newForm, validationSchema));
  };

  const onFieldBlur = (name: string) => {
    let schema = validationSchema.filter(x => x.name == name);

    if (schema.length > 0) {
      //@ts-ignore
      let error = ValidateField(form, schema[0]);
      setValidation({...validation, [name]: error});
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleSignUp = () => {
    navigation.navigate('SignUp');
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };
  const handleGoogleSingIn = async () => {
    setIsSocialLoginLoading(true);
    try {
      await GoogleSignin.hasPlayServices();
      const signInResp = await GoogleSignin.signIn();

      if (!signInResp || !signInResp.data || !signInResp.data.idToken) {
        return;
      }
      if (signInResp && signInResp.data.idToken && signInResp.data.user) {
        await signInWithGoogle(
          signInResp.data.idToken,
          signInResp.data.user.givenName!,
          signInResp.data.user.familyName!,
        );
        setError(undefined);
      } else {
        throw new Error('Google id token not found!');
      }
    } catch (error: any) {
      console.log('error', error);
      setError(error);
    } finally {
      setIsSocialLoginLoading(false);
    }
  };

  const handleAppleSingIn = async () => {
    setIsSocialLoginLoading(true);
    try {
      if (appleAuth.isSupported) {
        await handleAppleLogin();
      } else {
        Alert.alert('Apple auth is not supported in your device ');
      }
    } catch (error: any) {
      Alert.alert(error);
      setError(error);
    } finally {
      setIsSocialLoginLoading(false);
    }
  };

  const handleMicrosoftSingIn = async () => {
    setIsSocialLoginLoading(true);
    try {
      const response = await signInWithMicrosoft();
      setError(undefined);
    } catch (error: any) {
      setError(error);
    } finally {
      setIsSocialLoginLoading(false);
    }
  };

  const handleSignIn = () => {
    setIsLoading(true);
    signin(form.email, form.password)
      .then(() => {
        setError(undefined);
      })
      .catch(error => {
        if (error == 'auth/multi-factor-auth-required') {
          navigation.navigate('VerificationCode');
        } else {
          setError(error);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <KeyboardAvoidingView
      style={[styles.background, {backgroundColor: themeStyles.background}]}
      keyboardVerticalOffset={-500}
      behavior="padding">
      <ScrollView
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled">
        <TouchableWithoutFeedback onPress={() => dismissKeyboard()}>
          <View
            style={[
              styles.container,
              {backgroundColor: themeStyles.background},
            ]}>
            <Image
              source={require('../assets/images/logo-dark.png')}
              style={{width: iconSize * 2, height: iconSize}}
            />
            {!initializing ? (
              <View>
                <View style={styles.inputContainer}>
                  <EmailIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={themeStyles.text}
                    style={{marginLeft: 10}}
                  />
                  <TextInput
                    style={[styles.input, {color: themeStyles.text}]}
                    placeholder="Email"
                    placeholderTextColor={themeStyles.text}
                    onChangeText={text => onValueChange('email', text)}
                    value={form.email}
                    onEndEditing={() => onFieldBlur('email')}
                    inputMode="email"
                    keyboardType="email-address"
                    textContentType="emailAddress"
                    autoCapitalize="none"
                  />
                </View>
                {validation.email != '' && (
                  <Text style={styles.validationError}>{validation.email}</Text>
                )}

                <View style={styles.inputContainer}>
                  <LockIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={themeStyles.text}
                    style={{marginLeft: 10}}
                  />
                  <TextInput
                    style={[styles.input, {color: themeStyles.text}]}
                    placeholder="Password"
                    placeholderTextColor={themeStyles.text}
                    onChangeText={text => onValueChange('password', text)}
                    value={form.password}
                    onBlur={() => onFieldBlur('password')}
                    textContentType="password"
                    secureTextEntry={!revealPassword}
                  />
                  {revealPassword ? (
                    <TouchableOpacity
                      onPress={() => {
                        setRevealPassword(!revealPassword);
                      }}>
                      <EyeSlashIcon
                        width={inputIconSize}
                        height={inputIconSize}
                        fill={themeStyles.text}
                        style={{marginLeft: 5, marginRight: 5}}
                      />
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      onPress={() => {
                        setRevealPassword(!revealPassword);
                      }}>
                      <EyeIcon
                        width={inputIconSize}
                        height={inputIconSize}
                        fill={themeStyles.text}
                        style={{marginLeft: 5, marginRight: 5}}
                      />
                    </TouchableOpacity>
                  )}
                </View>
                {validation.password != '' && (
                  <Text style={styles.validationError}>
                    {validation.password}
                  </Text>
                )}

                <View style={styles.lowerContainer}>
                  <TouchableOpacity
                    style={[
                      styles.button,
                      !isFormValid
                        ? [
                            styles.signInDisabled,
                            {
                              backgroundColor: themeStyles.primaryDisabled,
                            },
                          ]
                        : [
                            styles.signIn,
                            {
                              backgroundColor: themeStyles.primary,
                              ...(theme === 'dark' && {
                                backgroundColor: 'transparent',
                                borderColor: themeStyles.text,
                                borderWidth: 1,
                              }),
                            },
                          ],
                      {marginBottom: 1},
                    ]}
                    onPress={handleSignIn}
                    disabled={!isFormValid}>
                    {!isLoading ? (
                      <Text style={styles.signInText}>Log In</Text>
                    ) : (
                      <ActivityIndicator color="white" />
                    )}
                  </TouchableOpacity>
                  {Platform.OS == 'ios' && (
                    <TouchableOpacity
                      style={[
                        styles.button,
                        styles.signIn,
                        // eslint-disable-next-line react-native/no-inline-styles
                        {
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginBottom: 10,
                          backgroundColor: themeStyles.primary,
                          ...(theme === 'dark' && {
                            borderColor: themeStyles.text,
                            borderWidth: 1,
                            backgroundColor: 'transparent',
                          }),
                        },
                      ]}
                      onPress={handleAppleSingIn}>
                      {!isSocialLoginLoading ? (
                        <>
                          <AppleIcon
                            width={inputIconSize}
                            height={inputIconSize}
                            style={styles.icon}
                            fill="white"
                          />
                          <Text style={styles.signInText}>
                            Continue With Apple
                          </Text>
                        </>
                      ) : (
                        <ActivityIndicator color="white" />
                      )}
                    </TouchableOpacity>
                  )}
                  {Platform.OS !== 'ios' && (
                    <TouchableOpacity
                      style={[
                        styles.button,
                        styles.signIn,
                        // eslint-disable-next-line react-native/no-inline-styles
                        {
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginBottom: 15,
                          backgroundColor: themeStyles.white,
                          borderColor: themeStyles.primary,
                          borderWidth: 1,
                          ...(theme === 'dark' && {
                            borderColor: themeStyles.text,
                            backgroundColor: 'transparent',
                          }),
                        },
                      ]}
                      onPress={handleGoogleSingIn}>
                      <GoogleIcon
                        width={inputIconSize}
                        height={inputIconSize}
                        style={styles.icon}
                      />
                      <Text
                        style={[
                          styles.signInText,
                          {color: themeStyles.primary},
                        ]}>
                        Continue With Google
                      </Text>
                      {isSocialLoginLoading && (
                        <ActivityIndicator color={themeStyles.primary} />
                      )}
                    </TouchableOpacity>
                  )}

                  {/* {
                    Platform.OS != 'ios' && (
                      <>
                        <View style={styles.socialContainer}>
                        <Text style={[styles.logInWith, { color: themeStyles.text }]}>
                         Login with Google
                        </Text>
                          {
                            Platform.OS == 'android' && (
                              <TouchableOpacity onPress={handleGoogleSingIn}>
                                <GoogleIcon
                                  width={inputIconSize}
                                  height={inputIconSize}
                                  style={styles.icon}
                                />
                              </TouchableOpacity>
                            )
                          }

                          {
                            Platform.OS == 'android' && (
                              <TouchableOpacity onPress={handleMicrosoftSingIn}>
                                <MicrosoftIcon
                                  width={inputIconSize}
                                  height={inputIconSize}
                                  style={styles.icon}
                                />
                              </TouchableOpacity>
                            )
                          }
                        </View>
                      </>
                    )
                  } */}
                  {error && <Text style={{color: 'red'}}>{error}</Text>}
                  <TouchableOpacity onPress={handleForgotPassword}>
                    <Text
                      style={[
                        styles.forgotPassword,
                        {color: themeStyles.text},
                      ]}>
                      Forgot Password?
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={handleSignUp}>
                    <Text style={styles.signUpContainer}>
                      <Text style={{color: themeStyles.text}}>
                        Don't have an account?
                      </Text>
                      <Text
                        style={[styles.signUp, {color: themeStyles.primary}]}>
                        {' '}
                        Sign Up
                      </Text>
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <Center>
                <ActivityIndicator size="large" color={themeStyles.primary} />
              </Center>
            )}
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  background: {
    backgroundColor: '#fff',
    flex: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  content: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  logo: {
    color: '#108a00',
    fontWeight: 'bold',
    fontSize: 50,
  },
  icon: {
    marginRight: 10,
  },
  signInText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 25,
    width: '100%',
    height: 45,
    marginTop: 20,
  },
  lowerContainer: {
    alignItems: 'center',
  },
  input: {
    flexGrow: 1,
    color: '#000',
    marginLeft: 10,
    height: 45,
  },
  logInWith: {
    marginTop: 10,
    textAlign: 'center',
  },
  forgotPassword: {
    textAlign: 'center',
  },
  signUpContainer: {
    marginTop: 20,
  },
  signUp: {
    marginTop: 30,
    textAlign: 'center',
    color: '#108a00',
    fontWeight: 'bold',
  },
  button: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  socialbutton: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    padding: 12,
    borderRadius: 25,
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    marginLeft: 15,
  },
  signIn: {
    backgroundColor: '#108a00',
  },
  signInDisabled: {
    backgroundColor: '#ccc',
  },
  notificationIconContainer: {
    alignSelf: 'center',
  },
  validationError: {
    marginTop: 5,
    marginLeft: 10,
    color: '#767676',
    fontWeight: 'bold',
  },
});

export default LoginScreen;
