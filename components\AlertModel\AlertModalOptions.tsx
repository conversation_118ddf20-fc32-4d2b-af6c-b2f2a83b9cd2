import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import CheckIcon from '../../assets/images/check-solid.svg';
import ClockIcon from '../../assets/images/clock-regular.svg';
import QuestionIcon from '../../assets/images/question-solid.svg';
import CrossIcon from '../../assets/images/x-solid.svg';
import {useApi} from '../../context/ApiContext';
import {useTheme} from '../../context/ThemeContext';

export default function AlertModalOptions({
  navigation,
  alert,
  onSelectScreen,
  alertCompleted,
  onAlertActioned,
  bypassTimeCheck = false,
}: any) {
  const {themeStyles} = useTheme();
  const {archiveAlert, updateAlert, refreshBadgeNumber} = useApi();

  const windowWidth = Dimensions.get('window').width;
  const inputIconSizeYes = 16;
  const inputIconSizeNo = 15;

  const [isActionLoading, setIsActionLoading] = useState<boolean>(false);
  const [timeToExpiryText, setTimeToExpiryText] = useState<string>('');
  const [isExpired, setExpiry] = useState<boolean>(false);
  // const [hasUpdatedNextAlert, setHasUpdatedNextAlert] = useState<boolean>(false);
  let timeDifference = 0;

  useEffect(() => {
    let timeDifferenceMs = new Date(alert.expiryDate).getTime() - new Date().getTime();

    const days = Math.floor(timeDifferenceMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDifferenceMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    let expired = false;
    let displayName: string = alert.categoryName;

    if (alert.subCategoryName) {
      displayName += ` - ${alert.subCategoryName}`;
    }

    let messageText = displayName || 'Alert';

    if (alert.companyName) {
      messageText += ` with ${alert.companyName}`;
    }

    if (timeDifferenceMs > 0) {
      if (days > 0) {
        messageText += ` is due to expire in ${days} ${days === 1 ? 'day' : 'days'}${hours > 0 ? ` and ${hours} ${hours === 1 ? 'hour' : 'hours'}` : ''}`;
      } else {
        messageText += ` is due to expire in ${hours} ${hours === 1 ? 'hour' : 'hours'}`;
      }
      expired = false;
    } else if (timeDifferenceMs < 0) {
      if (days > 0) {
        messageText += ` expired ${Math.abs(days)} ${Math.abs(days) === 1 ? 'day' : 'days'}${hours > 0 ? ` and ${Math.abs(hours)} ${Math.abs(hours) === 1 ? 'hour' : 'hours'}` : ''} ago`;
      } else {
        messageText += ` expired ${Math.abs(hours)} ${Math.abs(hours) === 1 ? 'hour' : 'hours'} ago`;
      }
      expired = true;
    } else {
      messageText += ` is going to expire today`;
      expired = false;
    }

    setExpiry(expired);
    setTimeToExpiryText(messageText);
  }, [alert]);

  // Cyclic alert functionality - update nextAlertDate when modal is shown
  // Skip for bypass alerts (push notifications)
  useEffect(() => {
    if (!bypassTimeCheck) {
      onUpdateNextAlertDate(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [alert, updateAlert, navigation, bypassTimeCheck]);

  const onAction = () => {
    setIsActionLoading(true);
    onAlertActioned();

    archiveAlert(alert.id)
      .then(() => {
        navigation.navigate('Alert', {alertId: alert.id, actioned: true});
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setIsActionLoading(false);

        refreshBadgeNumber();
        alertCompleted();
      });
  };

  const onAskAnExpert = () => {
    var nextDate = new Date();
    nextDate.setDate(new Date(alert.nextAlertDate).getDate() + 7);

    updateAlert({...alert, nextAlertDate: nextDate});

    onAlertActioned();
    alertCompleted();
    navigation.navigate('Expert', {
      categoryId: alert.categoryId,
      subCategoryId: alert.subCategoryId,
    });
  };

  const onDismiss = () => {
    if (bypassTimeCheck) {
      // For bypass alerts (push notifications), just complete without updating nextAlertDate
      onAlertActioned(); // This will close the modal
      alertCompleted();
    } else {
      // For regular alerts, update nextAlertDate
      onUpdateNextAlertDate(true);
    }
  };

 const onUpdateNextAlertDate = (isDismissed?: boolean) => {
  if (!alert || alert.defaultAlertTime === undefined) {
    if (isDismissed) {alertCompleted();}
    return;
  }

  // If defaultAlertTime is 0, set nextAlertDate to past expiry date so it won't come back
  if (alert.defaultAlertTime === 0) {
    const expiryDate = new Date(alert.expiryDate);
    // Set nextAlertDate to 1 day after expiry to ensure it won't show again
    const pastExpiryDate = new Date(expiryDate);
    pastExpiryDate.setDate(pastExpiryDate.getDate() + 1);

    const updatedAlert = {
      ...alert,
      nextAlertDate: pastExpiryDate,
    };

    updateAlert(updatedAlert)
      .then((res) => {
        if (isDismissed) {
          alertCompleted();
          navigation?.navigate?.('Home', { refresh: true });
        }
      })
      .catch((error) => {
        console.error('Error updating alert on dismiss:', error);
        if (isDismissed) {alertCompleted();}
      });
    return;
  }

  // For alerts with defaultAlertTime > 0, calculate the new nextAlertDate
  const currentNextAlertDate = new Date(alert.alertDate);
  const expiryDate = new Date(alert.expiryDate);
  const newNextAlertDate = new Date(currentNextAlertDate);
  newNextAlertDate.setDate(newNextAlertDate.getDate() + alert.defaultAlertTime);
  if (newNextAlertDate > expiryDate) {
    if (isDismissed) {alertCompleted();}
    return;
  }
  const updatedAlert = {
    ...alert,
    nextAlertDate: newNextAlertDate,
  };
  updateAlert(updatedAlert)
    .then((res) => {
      if (isDismissed) {
        alertCompleted();
        navigation?.navigate?.('Home', { refresh: true });
      }
    })
    .catch((error) => {
      console.error('Error updating alert on dismiss:', error);
      if (isDismissed) {alertCompleted();}
    });
};

  return (
    <View>
      <View style={[styles.modal, {backgroundColor: themeStyles.background}]}>
        <Text style={[styles.timeToExpiry,{color:themeStyles.text}]}>{timeToExpiryText}</Text>
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[styles.button, {backgroundColor: themeStyles.primary}]}
            onPress={onAction}>
            <View style={styles.iconContainer}>
              {!isActionLoading ? (
                <>
                  <CheckIcon
                    width={inputIconSizeYes}
                    height={inputIconSizeYes}
                    fill={themeStyles.background}
                  />
                  <Text
                    style={[
                      styles.confirmText,
                      {color: themeStyles.background},
                    ]}>
                    Sorted - enter new details
                  </Text>
                </>
              ) : (
                <ActivityIndicator
                  size="small"
                  color={themeStyles.background}
                />
              )}
            </View>
          </TouchableOpacity>
          {!isExpired && (
            <TouchableOpacity
              style={[styles.button, {backgroundColor: themeStyles.primary}]}
              onPress={() => onSelectScreen(2)}>
              <View style={styles.iconContainer}>
                <ClockIcon
                  width={inputIconSizeYes}
                  height={inputIconSizeYes}
                  fill={themeStyles.background}
                />
                <Text
                  style={[styles.confirmText, {color: themeStyles.background}]}>
                  Snooze
                </Text>
              </View>
            </TouchableOpacity>
          )}

          {alert.isExpertAvailable && (
            <TouchableOpacity
              style={[styles.button, {backgroundColor: themeStyles.primary}]}
              onPress={onAskAnExpert}>
              <View style={styles.iconContainer}>
                <QuestionIcon
                  width={inputIconSizeYes}
                  height={inputIconSizeYes}
                  fill={themeStyles.background}
                />
                <Text
                  style={[styles.confirmText, {color: themeStyles.background}]}>
                  Ask an expert
                </Text>
              </View>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[
              styles.button,
              styles.dismiss,
              {borderColor: themeStyles.text},
            ]}
            onPress={onDismiss}>
            <View style={styles.iconContainer}>
              <Text style={[styles.dismissText, {color: themeStyles.text}]}>
                Dismiss
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.button,
              styles.reject,
              {borderColor: themeStyles.text},
            ]}
            onPress={() => onSelectScreen(1)}>
            <View style={styles.iconContainer}>
              <CrossIcon
                width={inputIconSizeNo}
                height={inputIconSizeNo}
                fill={themeStyles.text}
              />
              <Text style={[styles.rejectText, {color: themeStyles.text}]}>
                Delete - No longer need this section
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        {alert.showFeedImage && (
          <View>
            <Text
              style={{
                textAlign: 'center',
                fontSize: 16,
                fontWeight: 'bold',
                color: themeStyles.text,
              }}>
              Don't pay the Loyalty Tax Man
            </Text>
            <Image
              source={require('../../assets/images/urgent-tax.png')}
              style={{
                aspectRatio: 1,
                resizeMode: 'contain',
                width: Dimensions.get('window').width < 800 ? '50%' : '25%',
                height: undefined,
                alignSelf: 'flex-end',
                marginBottom: Dimensions.get('window').width < 800 ? -20 : -80,
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  timeToExpiry: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  modal: {
    padding: 20,
    borderRadius: 8,
    width: '90%',
  },
  questionText: {
    fontSize: 16,
  },
  buttonsContainer: {
    marginTop: 15,
    flexDirection: 'column',
    justifyContent: 'space-evenly',
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    padding: 10,
    borderRadius: 25,
    height: 40,
    marginBottom: 10,
  },
  confirmText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  dismiss: {
    borderWidth: 2,
  },
  dismissText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  reject: {
    borderWidth: 2,
  },
  rejectText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -2,
  },
});
