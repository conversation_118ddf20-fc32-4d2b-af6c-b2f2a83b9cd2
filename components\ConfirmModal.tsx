import React from 'react'
import { StyleSheet, Modal, Text, View, TouchableOpacity, Dimensions } from 'react-native'
import CheckIcon from '../assets/images/check-solid.svg';
import CrossIcon from '../assets/images/x-solid.svg';
import BackArrow from '../assets/images/arrow-left-solid.svg';
import { useTheme } from '../context/ThemeContext';

type Props = {
    visible: boolean
    question: string,
    onConfirm: () => void,
    onReject: () => void,
    onBack?: () => void
}

export default function ConfirmModal({ visible, question, onConfirm, onReject, onBack }: Props) {

    const { themeStyles } = useTheme();

    const windowWidth = Dimensions.get('window').width;
    const inputIconSizeYes = 16;
    const inputIconSizeNo = 15;

    return (
        <Modal visible={visible} transparent={true}>
            <View style={styles.container}>
                <View style={[styles.modal, { backgroundColor: themeStyles.background }]}>
                    <View>
                        <Text style={[styles.questionText, { color: themeStyles.text}]}>{question}</Text>
                    </View>
                    <View style={[onBack ? { flexDirection: "column" } : { flexDirection: "row" }, styles.buttonsContainer]}>
                        <TouchableOpacity style={[styles.button, { backgroundColor: themeStyles.primary }, onBack ? { width: "100%" } : { width: "40%" }]} onPress={onConfirm}>
                            <View style={styles.iconContainer}>
                                <CheckIcon width={inputIconSizeYes} height={inputIconSizeYes} fill={themeStyles.background} />
                                <Text style={[styles.confirmText, { color: themeStyles.background }]}>Yes</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={[styles.button, styles.reject, { borderColor: themeStyles.text }, onBack ? { width: "100%" } : { width: "40%" }]} onPress={onReject}>
                            <View style={styles.iconContainer}>
                                <CrossIcon width={inputIconSizeNo} height={inputIconSizeNo} fill={themeStyles.text} />
                                <Text style={[styles.rejectText, { color: themeStyles.text }]}>No</Text>
                            </View>
                        </TouchableOpacity>
                        { onBack && (
                            <TouchableOpacity style={[styles.button, styles.reject, { borderColor: themeStyles.text }, { width: "100%"}]} onPress={onBack}>
                                <View style={styles.iconContainer}>
                                    <BackArrow width={inputIconSizeNo} height={inputIconSizeNo} fill={themeStyles.text} />
                                    <Text style={[styles.rejectText, { color: themeStyles.text }]}>Back</Text>
                                </View>
                            </TouchableOpacity>    
                        )}
                    </View>
                </View>
            </View>
        </Modal>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center'
    },
    modal: {
        padding: 20,
        borderRadius: 8,
        width: '90%'
    },
    questionText: {
        fontSize: 16,
    },
    buttonsContainer: {
        marginTop: 15,
        justifyContent: "space-evenly",
        alignItems: "center"
    },
    button: {
        padding: 10,
        borderRadius: 25,
        height: 40,
        marginBottom: 10
    },
    confirmText: {
        color: '#fff',
        fontWeight: "bold",
        textAlign: "center",
        marginLeft: 10
    },
    reject: {
        borderWidth: 2
    },
    rejectText: {
        color: '#767676',
        fontWeight: "bold",
        textAlign: "center",
        marginLeft: 10
    },
    iconContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: -2
    },
});