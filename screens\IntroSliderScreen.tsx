import { StackNavigationProp } from '@react-navigation/stack';
import React from 'react';
import { Dimensions, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AppIntroSlider from 'react-native-app-intro-slider';
import BusyIcon from '../assets/images/busy.svg';
import RelaxIcon from '../assets/images/relax.svg';
import TimeIcon from '../assets/images/time.svg';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

interface Slide {
    key: string;
    title: string;
    text: string;
}

type IntroSliderScreenNavigationProp = StackNavigationProp<RootStackParamList, 'IntroSlider'>;

type Props = {
    onCompleteSlider: () => void;
    navigation: IntroSliderScreenNavigationProp;
};

const slides = [
    {
        key: 'busy',
        title: 'Be financially alerted when you need to act',
        text: 'Never miss a deadline or let anything you need run out',
    },
    {
        key: 'time',
        title: 'Your portable filing cabinet',
        text: 'Simple access to important personal documentation and financial products on the go',
    },
    {
        key: 'relax',
        title: 'Save money by renewing at the right time',
        text: 'Guidance on the best time to review insurances',
    },
];

const IntroSliderScreen: React.FC<Props> = ({ navigation, onCompleteSlider }) => {
    const windowWidth = Dimensions.get('window').width;
    const windowHeight = Dimensions.get('window').height;
    const iconSize = windowWidth * 0.3;
    const { themeStyles ,theme} = useTheme();

    const renderItem = ({ item }: { item: Slide }) => (
        <View style={{ alignItems: 'center'}}>
            {
                item.key == 'busy' ?
                    <BusyIcon width={iconSize} height={iconSize} fill={themeStyles.background} /> : null
            }
            {
                item.key == 'time' ?
                    <TimeIcon width={iconSize} height={iconSize} fill={themeStyles.background} /> : null
            }
            {
                item.key == 'relax' ?
                    <RelaxIcon width={iconSize} height={iconSize} fill={themeStyles.background} /> : null
            }
            <View style={{ paddingLeft: 20, paddingRight: 20 }}>
                <Text style={[styles.title, { color: themeStyles.text }]}>{item.title}</Text>
                <Text style={[styles.text, { color: themeStyles.text }]}>{item.text}</Text>
            </View>
        </View>
    );

    return (
      <View
        style={{
          flex: 1,
          backgroundColor: themeStyles.background,
          paddingVertical: windowHeight * 0.05,
        }}>
        <View
          style={[styles.container, {backgroundColor: themeStyles.background}]}>
          <Image
            source={require('../assets/images/logo-dark.png')}
            style={{width: iconSize * 2, height: iconSize}}
          />
        </View>
        <AppIntroSlider
          data={slides}
          renderItem={renderItem}
          activeDotStyle={{backgroundColor: themeStyles.primary}}
          dotStyle={{backgroundColor: themeStyles.primaryDisabled}}
          style={{backgroundColor: themeStyles.background}}
          showDoneButton={false}
          showNextButton={false}
          showSkipButton={false}
        />
        <TouchableOpacity
          style={[styles.btn, {backgroundColor: themeStyles.primary}]}
          onPress={onCompleteSlider}>
          <Text style={[styles.btnText, {color: themeStyles.background}]}>
            Login
          </Text>
        </TouchableOpacity>
      </View>
    );
};

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '25%'
    },
    logo: {
        color: '#108a00',
        fontWeight: "bold",
        fontSize: 50
    },
    title: {
        fontWeight: "bold",
        fontSize: 30,
        color: '#000',
        textAlign: 'center'
    },
    text: {
        fontSize: 15,
        color: '#000',
        marginTop: 15,
        textAlign: 'center'
    },
    btn: {
        margin: 20,
        width: '90%',
        backgroundColor: '#108a00',
        padding: 15,
        borderRadius: 25
    },
    btnText: {
        color: '#fff',
        fontWeight: "bold",
        textAlign: "center"
    }
});

export default IntroSliderScreen;