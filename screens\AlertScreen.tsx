import DateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Keyboard,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  CameraOptions,
  ImageLibraryOptions,
  launchCamera,
  launchImageLibrary,
} from 'react-native-image-picker';
import { PERMISSIONS, RESULTS, check, request } from 'react-native-permissions';
import { CategoryType } from '../api/types/CategoryType';
import AlertIcon from '../assets/images/bell-solid.svg';
import CalendarIcon from '../assets/images/calendar-days-regular.svg';
import CameraIcon from '../assets/images/camera-solid.svg';
import SaveIcon from '../assets/images/floppy-disk-solid.svg';
import ImagesIcon from '../assets/images/images-solid.svg';
import DeleteIcon from '../assets/images/trash-solid.svg';
import CrossIcon from '../assets/images/x-solid.svg';
import { AlertModel } from '../common/AlertModel';
import { generateGuid } from '../common/GuidGenerator';
import { FileType } from '../common/IAlert';
import { ListItem } from '../common/ListItem';
import {
  FieldValidation,
  ValidateField,
  ValidateForm,
} from '../common/Validation';
import { formatDate } from '../common/helpers';
import ConfirmModal from '../components/ConfirmModal';
import Document from '../components/Document';
import ListModal from '../components/ListModal';
import WarningModal from '../components/WarningModal';
import { useApi } from '../context/ApiContext';
import { useRefresh } from '../context/RefreshContex';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

// Constants
const ICON_SIZE = 16;
const DEFAULT_ALERT_TIME = 30;
const CATEGORIES_THAT_HIDE_COMPANY = [
  'Events',
  'Tax',
  'MOT',
  'Driving License',
  'Passport',
  'Vaccination',
];

// Types
interface RouteParams {
  alertId?: string;
  restore?: boolean;
  actioned?: boolean;
  categoryId?: string;
  subCategoryId?: string;
}

type AlertScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Alert'
>;

interface Props {
  navigation: AlertScreenNavigationProp;
}

export interface ImageType {
  id: string;
  name: string;
  type: string;
  uri: string;
}

interface ValidationState {
  id: string;
  categoryId: string;
  categoryName: string;
  subCategoryId: string;
  subCategoryName: string;
  time: string;
  isUrgent: string;
  policyNumber: string;
  companyName: string;
  expiryDate: string;
  alertDate: string;
  notes: string;
  documents: string;
}

// Custom hooks
const usePermissions = () => {
  const checkCameraPermission = useCallback(async (): Promise<boolean> => {
    const permission =
      Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.CAMERA
        : PERMISSIONS.IOS.CAMERA;

    try {
      const result = await check(permission);

      switch (result) {
        case RESULTS.GRANTED:
        case RESULTS.LIMITED:
          return true;
        case RESULTS.DENIED: {
          const requestResult = await request(permission);
          return (
            requestResult === RESULTS.GRANTED ||
            requestResult === RESULTS.LIMITED
          );
        }
        default:
          return false;
      }
    } catch (error) {
      console.error('Camera permission error:', error);
      return false;
    }
  }, []);

  const checkPhotoLibraryPermission =
    useCallback(async (): Promise<boolean> => {
      if (Platform.OS === 'android') {
        return true;
      }

      try {
        const result = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);

        switch (result) {
          case RESULTS.GRANTED:
          case RESULTS.LIMITED:
            return true;
          case RESULTS.DENIED:
            const requestResult = await request(PERMISSIONS.IOS.PHOTO_LIBRARY);
            return (
              requestResult === RESULTS.GRANTED ||
              requestResult === RESULTS.LIMITED
            );
          default:
            return false;
        }
      } catch (error) {
        console.error('Photo library permission error:', error);
        return false;
      }
    }, []);

  return {checkCameraPermission, checkPhotoLibraryPermission};
};

const useFormValidation = (form: AlertModel, categories: CategoryType[]) => {
  const validationSchema: FieldValidation[] = useMemo(
    () => [
      {
        name: 'categoryId',
        requirements: ['Required'],
        displayName: 'Category',
      },
      {
        name: 'subCategoryId',
        requirements: ['RequiredIf'],
        displayName: 'Subcategory',
        options: {
          field: 'subCategoryIsRequired',
          value: true,
          operator: 'equal',
        },
      },
      {
        name: 'policyNumber',
        requirements: ['RequiredOr'],
        displayName: 'Reference number',
        fields: [
          {fieldName: 'policyNumber', displayName: 'Reference number'},
          {fieldName: 'companyName', displayName: 'Company name'},
        ],
      },
      {
        name: 'companyName',
        requirements: ['RequiredOr'],
        displayName: 'Company name',
        fields: [
          {fieldName: 'policyNumber', displayName: 'Reference number'},
          {fieldName: 'companyName', displayName: 'Company name'},
        ],
      },
    ],
    [],
  );

  const shouldShowCompanyName = useMemo(() => {
    const category = categories.find(c => c.categoryId === form.categoryId);
    return (
      !CATEGORIES_THAT_HIDE_COMPANY.includes(form.categoryName) &&
      !category?.isCustomCategory
    );
  }, [form.categoryName, form.categoryId, categories]);

  const isFormValid = useMemo(() => {
    if (
      new Date(form.expiryDate).getTime() < new Date(form.alertDate).getTime()
    ) {
      return false;
    }
    return ValidateForm(form, validationSchema);
  }, [form, validationSchema]);

  return {validationSchema, shouldShowCompanyName, isFormValid};
};

// Memoized components
const FormField = React.memo<{
  label: string;
  children: React.ReactNode;
  error?: string;
}>(({label, children, error}) => {
  const {themeStyles} = useTheme();

  return (
    <View>
      <Text style={[styles.label, {color: themeStyles.text}]}>{label}</Text>
      {children}
      {error && <Text style={styles.validationError}>{error}</Text>}
    </View>
  );
});

const CustomTextInput = React.memo<{
  value: string;
  onChangeText: (text: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  multiline?: boolean;
  numberOfLines?: number;
  editable?: boolean;
  style?: any;
}>(
  ({
    value,
    onChangeText,
    onBlur,
    placeholder,
    multiline,
    numberOfLines,
    editable = true,
    style,
  }) => {
    const {themeStyles, theme} = useTheme();

    return (
      <TextInput
        style={[
          multiline ? styles.inputNotes : styles.input,
          {
            color: themeStyles.text,
            backgroundColor: themeStyles.background,
          },
          style,
        ]}
        onChangeText={onChangeText}
        value={value}
        placeholder={placeholder}
        placeholderTextColor={theme === 'dark' ? '#fff' : 'gray'}
        onBlur={onBlur}
        multiline={multiline}
        numberOfLines={numberOfLines}
        textAlignVertical={multiline ? 'top' : 'center'}
        editable={editable}
      />
    );
  },
);

const ActionButton = React.memo<{
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon: React.ComponentType<any>;
  text: string;
  variant?: 'primary' | 'secondary' | 'danger';
  style?: any;
}>(
  ({
    onPress,
    disabled,
    loading,
    icon: Icon,
    text,
    variant = 'primary',
    style,
  }) => {
    const {themeStyles} = useTheme();

    const buttonStyles = useMemo(() => {
      switch (variant) {
        case 'secondary':
          return {
            backgroundColor: themeStyles.background,
            borderWidth: 2,
            borderColor: themeStyles.primary,
          };
        case 'danger':
          return {
            backgroundColor: 'transparent',
            borderWidth: 2,
            borderColor: themeStyles.danger,
          };
        default:
          return {
            backgroundColor: disabled
              ? themeStyles.primaryDisabled
              : themeStyles.primary,
          };
      }
    }, [variant, disabled, themeStyles]);

    const textColor =
      variant === 'primary'
        ? themeStyles.background
        : variant === 'danger'
        ? themeStyles.danger
        : themeStyles.primary;

    return (
      <TouchableOpacity
        style={[
          variant === 'danger' ? styles.archive : styles.btn,
          buttonStyles,
          style,
        ]}
        onPress={onPress}
        disabled={disabled || loading}>
        {loading ? (
          <ActivityIndicator color={themeStyles.background} />
        ) : (
          <View style={styles.iconContainer}>
            <Icon width={ICON_SIZE} height={ICON_SIZE} fill={textColor} />
            <Text style={[styles.iconText, {color: textColor}]}>{text}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  },
);

const DocumentPreview = React.memo<{
  documents: FileType[];
  onDeleteDocument: (id: string) => void;
}>(({documents, onDeleteDocument}) => {
  const {themeStyles} = useTheme();

  if (!documents.length) return null;

  return (
    <View>
      {documents.map((document, index) => (
        <View style={styles.previewPhoto} key={index}>
          <Document documentName={document.fileName} width={200} height={200} />
          <TouchableOpacity
            style={[
              styles.deleteButton,
              {
                backgroundColor: themeStyles.danger,
                borderColor: themeStyles.danger,
              },
            ]}
            onPress={() => onDeleteDocument(document.id)}>
            <CrossIcon
              width={ICON_SIZE}
              height={ICON_SIZE}
              fill={themeStyles.background}
            />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
});

const AlertScreen: React.FC<Props> = ({navigation}) => {
  const route = useRoute();
  const {alertId, restore, actioned, categoryId, subCategoryId}: RouteParams =
    route.params ?? {};

  const {
    getAlert,
    createAlert,
    updateAlert,
    archiveAlert,
    deleteAlert,
    getCategories,
    saveDocuments,
  } = useApi();

  const {themeStyles, theme} = useTheme();
  const {refreshScreens} = useRefresh();
  const {checkCameraPermission, checkPhotoLibraryPermission} = usePermissions();

  // State
  const [form, setForm] = useState<AlertModel>(() => ({
    id: '',
    categoryId: categoryId || '',
    categoryName: '',
    subCategoryId: subCategoryId || '',
    subCategoryName: '',
    time: '',
    isUrgent: false,
    policyNumber: '',
    companyName: '',
    expiryDate: new Date(new Date().setHours(23, 59, 59, 999)),
    alertDate: new Date(),
    nextAlertDate: new Date(),
    notes: '',
    circleColor: themeStyles.primary,
    lineColor: themeStyles.primary,
    documents: [],
    isArchived: false,
    status: 0,
    iconName: '',
    subCategoryIsRequired: false,
    showFeedImage: false,
    defaultAlertTime: 0,
  }));

  const [validation, setValidation] = useState<ValidationState>({
    id: '',
    categoryId: '',
    categoryName: '',
    subCategoryId: '',
    subCategoryName: '',
    time: '',
    isUrgent: '',
    policyNumber: '',
    companyName: '',
    expiryDate: '',
    alertDate: '',
    notes: '',
    documents: '',
  });

  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<ListItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<
    CategoryType | undefined
  >(undefined);
  const [isAlertDateChangedManually, setIsAlertDateChangedManually] =
    useState(false);
  const [images, setImages] = useState<ImageType[]>([]);

  // UI State
  const [isLoading, setIsLoading] = useState(false);
  const [showExpiryDatePicker, setShowExpiryDatePicker] = useState(false);
  const [showAlertDatePicker, setShowAlertDatePicker] = useState(false);
  const [showAlertTimePicker, setShowAlertTimePicker] = useState(false);
  const [showCategoryList, setShowCategoryList] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isError, setIsError] = useState(false);
  const [showCameraWarningModal, setShowCameraWarningModal] = useState(false);

  const {validationSchema, shouldShowCompanyName, isFormValid} =
    useFormValidation(form, categories);

  // Memoized values
  const isEditMode = useMemo(
    () => Boolean(alertId && !actioned),
    [alertId, actioned],
  );

  // Data fetching
  const fetchCategories = useCallback(async () => {
    try {
      const result = await getCategories(false);
      setCategories(result);
      const options: ListItem[] = result.map((category: CategoryType) => ({
        id: category.categoryId,
        name: category.name,
      }));
      setCategoryOptions(options);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, [getCategories]);
  const computeDates = (
    baseDate: Date,
    defaultAlertTime: number,
    isAlertDateChangedManually: boolean,
  ) => {
    // expiry
    const expiryDate = new Date(baseDate);
    if (defaultAlertTime > 0) {
      expiryDate.setDate(expiryDate.getDate() + defaultAlertTime);
    }
    expiryDate.setHours(23, 59, 59, 999);

    // alert
    let alertDate: Date | undefined;
    if (!isAlertDateChangedManually) {
      alertDate = new Date(expiryDate);
      if (defaultAlertTime > 0) {
        alertDate.setDate(alertDate.getDate() - defaultAlertTime);
      }
      alertDate.setHours(10, 0, 0, 0);
    }

    return {expiryDate, alertDate};
  };

  //For category changes
  const handleCategoryChange = useCallback(
    (selectedCategoryId: string) => {
      if (selectedCategoryId) {
        const category = categories.find(
          c => c.categoryId === selectedCategoryId,
        );

        if (category) {
          const {expiryDate, alertDate} = computeDates(
            new Date(),
            category.defaultAlertTime,
            isAlertDateChangedManually,
          );

          setForm(prev => ({
            ...prev,
            categoryId: category.categoryId,
            categoryName: category.name,
            subCategoryId: '',
            subCategoryName: '',
            showFeedImage: category.showFeedImage ?? false,
            defaultAlertTime: category.defaultAlertTime,
            expiryDate,
            ...(alertDate ? {alertDate} : {}), // only override if recomputed
          }));
        }

        setSelectedCategory(category);
      } else {
        setSelectedCategory(undefined);
        setForm(prev => ({
          ...prev,
          categoryId: '',
          categoryName: '',
          subCategoryId: '',
          subCategoryName: '',
          showFeedImage: false,
          defaultAlertTime: 0,
          expiryDate: new Date(),
          alertDate: new Date(),
        }));
      }
    },
    [categories, isAlertDateChangedManually],
  );

  const onFieldBlur = useCallback(
    (name: string, value?: any) => {
      const schema = validationSchema.find(x => x.name === name);
      if (!schema) {
        return;
      }
      const formToValidate = {...form};
      if (value !== undefined) {
        (formToValidate as any)[name] = value;
      }
      const error = ValidateField(formToValidate, schema);
      if (name === 'policyNumber' || name === 'companyName') {
        if (!form.policyNumber && !form.companyName) {
          setValidation(prev => ({
            ...prev,
            policyNumber: 'Either Reference or Company Name is required.',
            companyName: 'Either Reference or Company Name is required.',
          }));
        } else {
          setValidation(prev => ({
            ...prev,
            policyNumber: '',
            companyName: '',
          }));
        }
      } else if (name === 'alertDate') {
        let dateError = error;
        if (new Date(form.expiryDate) < new Date(value)) {
          dateError = 'Alert date must be before expiry date';
        }
        setValidation(prev => ({
          ...prev,
          alertDate: dateError,
          expiryDate: '',
        }));
      } else {
        setValidation(prev => ({...prev, [name]: error}));
      }
    },
    [form, validationSchema],
  );

  // Date/Time handlers
  const handleExpiryDateChange = useCallback(
    (selectedDate?: Date) => {
      setShowExpiryDatePicker(false);

      if (selectedDate && selectedCategory) {
        const {expiryDate, alertDate} = computeDates(
          selectedDate,
          selectedCategory.defaultAlertTime,
          isAlertDateChangedManually,
        );

        setForm(prev => ({
          ...prev,
          expiryDate,
          ...(alertDate ? {alertDate} : {}),
        }));

        onFieldBlur('expiryDate', selectedDate);
      }
    },
    [selectedCategory, isAlertDateChangedManually, onFieldBlur],
  );

  // Form handlers
  const onValueChange = useCallback(
    (name: string, value: any) => {
      switch (name) {
        case 'categoryId':
          handleCategoryChange(value);
          break;
        case 'expiryDate': {
          handleExpiryDateChange(new Date(value));
          break;
        }

        case 'alertDate': {
          setForm(prevForm => {
            const newForm = {
              ...prevForm,
              [name]: new Date(value),
            };
            return newForm;
          });
          onFieldBlur('alertDate', new Date(value));
          setIsAlertDateChangedManually(true);
          setShowAlertTimePicker(true);
          break;
        }
        default:
          setForm(prevForm => ({
            ...prevForm,
            [name]: value,
          }));
          break;
      }
    },
    [handleExpiryDateChange, handleCategoryChange, onFieldBlur],
  );

  const handleAlertDateChange = useCallback(
    (event: DateTimePickerEvent, selectedDate?: Date) => {
      setShowAlertDatePicker(false);

      if (selectedDate) {
        onValueChange('alertDate', selectedDate.toString());
        onFieldBlur('alertDate', selectedDate);
        setShowAlertTimePicker(true);
      }
    },
    [onValueChange, onFieldBlur],
  );

  const handleAlertTimeChange = useCallback(
    (_: any, selectedTime?: Date) => {
      setShowAlertTimePicker(false);

      if (selectedTime) {
        const updatedDate = new Date(form.alertDate);
        updatedDate.setHours(selectedTime.getHours());
        updatedDate.setMinutes(selectedTime.getMinutes());

        setForm(prev => ({...prev, alertDate: updatedDate}));
        onFieldBlur('alertDate', updatedDate);
      }
    },
    [form.alertDate, onFieldBlur],
  );

  // Image handling
  const handleCameraResult = useCallback((result: any) => {
    if (result?.assets?.[0]) {
      const id = generateGuid();
      const asset = result.assets[0];

      const newImage: ImageType = {
        id,
        name: asset.fileName,
        type: asset.type,
        uri: asset.uri,
      };

      const newDocument: FileType = {
        id,
        fileName: asset.uri,
      };

      setImages(prev => [...prev, newImage]);
      setForm(prev => ({
        ...prev,
        documents: [...prev.documents, newDocument],
      }));
    }
  }, []);

  const handleLibraryResult = useCallback((result: any) => {
    if (result?.assets && !result.didCancel) {
      const newImages: ImageType[] = [];
      const newDocuments: FileType[] = [];

      result.assets.forEach((asset: any) => {
        const id = generateGuid();
        newImages.push({
          id,
          name: asset.fileName,
          type: asset.type,
          uri: asset.uri,
        });
        newDocuments.push({
          id,
          fileName: asset.uri,
        });
      });

      setImages(prev => [...prev, ...newImages]);
      setForm(prev => ({
        ...prev,
        documents: [...prev.documents, ...newDocuments],
      }));
    }
  }, []);

  const toggleCamera = useCallback(async () => {
    const hasPermission = await checkCameraPermission();

    if (!hasPermission) {
      setShowCameraWarningModal(true);
      return;
    }

    const options: CameraOptions = {
      mediaType: 'photo',
      saveToPhotos: true,
      includeBase64: true,
    };

    launchCamera(options, handleCameraResult);
  }, [checkCameraPermission, handleCameraResult]);

  const toggleImageLibrary = useCallback(async () => {
    const hasPermission = await checkPhotoLibraryPermission();

    if (!hasPermission) {
      setShowCameraWarningModal(true);
      return;
    }

    const options: ImageLibraryOptions = {
      mediaType: 'photo',
      selectionLimit: 0,
    };

    launchImageLibrary(options, handleLibraryResult);
  }, [checkPhotoLibraryPermission, handleLibraryResult]);

  const deletePhoto = useCallback((idToDelete: string) => {
    setImages(prev => prev.filter(image => image.id !== idToDelete));
    setForm(prev => ({
      ...prev,
      documents: prev.documents.filter(doc => doc.id !== idToDelete),
    }));
  }, []);

  // CRUD operations
  const createNewAlert = useCallback(async () => {
    setIsLoading(true);
    try {
      const alertToCreate = {...form, nextAlertDate: form.alertDate};
      const result = await createAlert(alertToCreate);

      if (images.length > 0) {
        await saveDocuments(result.id, images);
      }

      navigation.navigate('Home', {refresh: true});
      setIsError(false);
    } catch (error) {
      console.error('Error creating alert:', error);
      setIsError(true);
    } finally {
      setIsLoading(false);
      refreshScreens(['Home', 'Documents']);
    }
  }, [form, images, createAlert, saveDocuments, navigation, refreshScreens]);

  const saveCurrentAlert = useCallback(async () => {
    setIsLoading(true);
    try {
      const alertToUpdate = {...form, nextAlertDate: form.alertDate};
      await updateAlert(alertToUpdate);

      if (images.length > 0) {
        await saveDocuments(form.id, images);
      }

      navigation.navigate('Home', {refresh: true});
      setIsError(false);
    } catch (error) {
      console.error('Error updating alert:', error);
      setIsError(true);
    } finally {
      setIsLoading(false);
      refreshScreens(['Home', 'Documents']);
    }
  }, [form, images, updateAlert, saveDocuments, navigation, refreshScreens]);

  const onConfirmArchive = useCallback(async () => {
    try {
      await archiveAlert(form.id);
      navigation.navigate('Home', {refresh: true});
    } catch (error) {
      console.error('Error archiving alert:', error);
    } finally {
      setShowDeleteModal(false);
      refreshScreens(['Home', 'Documents']);
    }
  }, [form.id, archiveAlert, navigation, refreshScreens]);

  const onDeleteAlertWithoutArchive = useCallback(async () => {
    try {
      await deleteAlert(form.id);
      navigation.navigate('Home', {refresh: true});
    } catch (error) {
      console.error('Error deleting alert:', error);
    } finally {
      refreshScreens(['Home', 'Documents']);
    }
  }, [form.id, deleteAlert, navigation, refreshScreens]);

  // Effects
  useEffect(() => {
    const loadAlert = async () => {
      if (!alertId) {
        setIsLoading(false);
        fetchCategories();
        return;
      }

      setIsLoading(true);
      try {
        const alert = await getAlert(alertId);
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);

        const alertModel = new AlertModel({
          id: actioned ? '' : alert.id,
          categoryId: alert.categoryId,
          categoryName: alert.categoryName,
          subCategoryId: alert.subCategoryId,
          subCategoryName: alert.subCategoryName,
          time: alert.time,
          isUrgent: alert.isUrgent,
          policyNumber: alert.policyNumber,
          companyName: alert.companyName,
          expiryDate: restore ? tomorrow : new Date(alert.expiryDate),
          alertDate: restore ? tomorrow : new Date(alert.alertDate),
          nextAlertDate: restore ? tomorrow : new Date(alert.nextAlertDate),
          notes: alert.notes,
          circleColor: alert.circleColor,
          lineColor: alert.lineColor,
          documents: actioned ? [] : alert.documents,
          isArchived: false,
          status: alert.status,
          iconName: alert.iconName,
          subCategoryIsRequired: false,
          showFeedImage: alert.showFeedImage,
          defaultAlertTime: alert.defaultAlertTime,
        });

        setForm(alertModel);
      } catch (error) {
        console.error('Error loading alert:', error);
      } finally {
        setIsLoading(false);
        fetchCategories();
      }
    };

    loadAlert();
  }, [alertId, actioned, restore, getAlert, fetchCategories]);

  // Update category name when options change
  useEffect(() => {
    if (form.categoryId) {
      handleCategoryChange(form.categoryId);
      // const selectedCategory = categoryOptions.find(
      //   cat => cat.id === form.categoryId,
      // );
      // if (selectedCategory && selectedCategory.name !== form.categoryName) {
      //   setForm(prev => ({...prev, categoryName: selectedCategory.name}));
      // }
    }
  }, [form.categoryId, handleCategoryChange]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={themeStyles.primary} />
      </View>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={[
        styles.scrollContainer,
        {backgroundColor: themeStyles.background},
      ]}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.childContainer}>
          {/* Modals */}
          <ConfirmModal
            visible={showDeleteModal}
            onConfirm={onConfirmArchive}
            onReject={onDeleteAlertWithoutArchive}
            question="Do you want to archive this alert?"
            onBack={() => setShowDeleteModal(false)}
          />

          <WarningModal
            visible={isError}
            warning="Unable to save alert"
            onConfirm={() => setIsError(false)}
          />

          <WarningModal
            visible={showCameraWarningModal}
            warning="Flerts does not have access to the camera, it can be enabled in your device settings"
            onConfirm={() => setShowCameraWarningModal(false)}
          />

          {/* Category Selection */}
          <FormField label="Category" error={validation.categoryId}>
            <TouchableOpacity
              onPress={() => setShowCategoryList(true)}
              style={[
                styles.inputDropdown,
                {backgroundColor: themeStyles.background},
              ]}>
              <Text
                style={[styles.inputDropdownLabel, {color: themeStyles.text}]}>
                {form.categoryName || 'Select one'}
              </Text>
            </TouchableOpacity>
            <ListModal
              dataList={categoryOptions}
              visible={showCategoryList}
              onClose={() => setShowCategoryList(false)}
              onItemSelect={item => {
                onValueChange('categoryId', item);
                setShowCategoryList(false);
              }}
              selectedItem={form.categoryId}
            />
          </FormField>

          {/* Reference Field */}
          <FormField label="Reference" error={validation.policyNumber}>
            {shouldShowCompanyName && (
              <CustomTextInput
                value={form.policyNumber}
                onChangeText={value => onValueChange('policyNumber', value)}
                onBlur={() => onFieldBlur('policyNumber')}
                placeholder="i.e. 857451"
              />
            )}
          </FormField>

          {/* Company Name Field */}
          {shouldShowCompanyName && (
            <FormField label="Company Name" error={validation.companyName}>
              <CustomTextInput
                value={form.companyName}
                onChangeText={value => onValueChange('companyName', value)}
                onBlur={() => onFieldBlur('companyName')}
                placeholder="Enter Company name"
              />
            </FormField>
          )}

          {/* Company Name for non-showing company categories */}
          {!shouldShowCompanyName && (
            <FormField label="Company Name" error={validation.companyName}>
              <CustomTextInput
                value={form.companyName}
                onChangeText={value => onValueChange('companyName', value)}
                onBlur={() => onFieldBlur('companyName')}
                placeholder="Enter Company name"
              />
            </FormField>
          )}

          {/* Notes Field */}
          <FormField label="Note to self">
            <CustomTextInput
              value={form.notes}
              onChangeText={value => onValueChange('notes', value)}
              placeholder="i.e. Birthday Event"
              multiline
              numberOfLines={3}
            />
          </FormField>

          {/* Expiry Date */}
          <FormField label="Expiry Date" error={validation.expiryDate}>
            <View style={styles.groupContainer}>
              {Platform.OS === 'ios' ? (
                <View style={styles.datePickerContainer}>
                  <DateTimePicker
                    value={form.expiryDate}
                    mode="date"
                    display="default"
                    minimumDate={new Date()}
                    onChange={(_: any, selectedDate?: Date) => {
                      if (selectedDate) {
                        onValueChange('expiryDate', selectedDate);
                      }
                    }}
                    themeVariant={theme}
                  />
                </View>
              ) : (
                <>
                  <CustomTextInput
                    value={formatDate(form.expiryDate)}
                    onChangeText={() => {}}
                    editable={false}
                    style={[styles.sideBySide, styles.center]}
                  />
                  <TouchableOpacity
                    style={[
                      styles.btn,
                      styles.sideBySide,
                      styles.btnDate,
                      {backgroundColor: themeStyles.primary},
                    ]}
                    onPress={() => setShowExpiryDatePicker(true)}>
                    <View style={styles.iconContainer}>
                      <CalendarIcon
                        width={ICON_SIZE}
                        height={ICON_SIZE}
                        fill={themeStyles.background}
                      />
                      <Text
                        style={[
                          styles.iconText,
                          {color: themeStyles.background},
                        ]}>
                        Date
                      </Text>
                    </View>
                  </TouchableOpacity>
                  {showExpiryDatePicker && (
                    <DateTimePicker
                      value={form.expiryDate}
                      minimumDate={new Date()}
                      mode="date"
                      display="default"
                      onChange={(_: any, selectedDate?: Date) => {
                        if (selectedDate) {
                          onValueChange('expiryDate', selectedDate);
                        }
                      }}
                    />
                  )}
                </>
              )}
            </View>
          </FormField>

          {/* Alert Date */}
          <FormField label="Alert Date" error={validation.alertDate}>
            <View style={styles.groupContainer}>
              {Platform.OS === 'ios' ? (
                <View style={styles.dateTimePickerContainer}>
                  <DateTimePicker
                    value={form.alertDate}
                    mode="datetime"
                    display="default"
                    onChange={(_: any, selectedDate?: Date) => {
                      if (selectedDate) {
                        onValueChange('alertDate', selectedDate);
                      }
                    }}
                    themeVariant={theme}
                  />
                </View>
              ) : (
                <>
                  <CustomTextInput
                    value={formatDate(form.alertDate, 'alert')}
                    onChangeText={() => {}}
                    editable={false}
                    style={[styles.sideBySide, styles.center]}
                  />
                  <TouchableOpacity
                    style={[
                      styles.btn,
                      styles.sideBySide,
                      styles.btnDate,
                      {backgroundColor: themeStyles.primary},
                    ]}
                    onPress={() => setShowAlertDatePicker(true)}>
                    <View style={styles.iconContainer}>
                      <CalendarIcon
                        width={ICON_SIZE}
                        height={ICON_SIZE}
                        fill={themeStyles.background}
                      />
                      <Text
                        style={[
                          styles.iconText,
                          {color: themeStyles.background},
                        ]}>
                        Date
                      </Text>
                    </View>
                  </TouchableOpacity>
                  {showAlertDatePicker && (
                    <DateTimePicker
                      value={form.alertDate}
                      mode="date"
                      display="default"
                      onChange={(_: any, selectedDate?: Date) => {
                        if (selectedDate) {
                          onValueChange('alertDate', selectedDate);
                        }
                      }}
                    />
                  )}
                  {showAlertTimePicker && (
                    <DateTimePicker
                      value={form.alertDate}
                      mode="time"
                      display="default"
                      onChange={handleAlertTimeChange}
                    />
                  )}
                </>
              )}
            </View>
          </FormField>

          {/* Documents Section */}
          <FormField label="Documents">
            <View style={styles.groupContainer}>
              <ActionButton
                onPress={toggleCamera}
                icon={CameraIcon}
                text="Camera"
                style={styles.btnSideBySide}
              />
              <ActionButton
                onPress={toggleImageLibrary}
                icon={ImagesIcon}
                text="Image library"
                variant="secondary"
                style={styles.btnSideBySide}
              />
            </View>
          </FormField>

          {/* Document Preview */}
          <DocumentPreview
            documents={form.documents}
            onDeleteDocument={deletePhoto}
          />

          {/* Action Buttons */}
          <ActionButton
            onPress={isEditMode ? saveCurrentAlert : createNewAlert}
            disabled={!isFormValid}
            loading={isLoading}
            icon={isEditMode ? SaveIcon : AlertIcon}
            text={isEditMode ? 'Save' : 'Add Alert'}
          />

          {/* Delete/Archive Button */}
          {form.id && !restore && (
            <ActionButton
              onPress={() => setShowDeleteModal(true)}
              icon={DeleteIcon}
              text="Delete"
              variant="danger"
            />
          )}
        </View>
      </TouchableWithoutFeedback>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 100,
  },
  childContainer: {
    marginTop: 10,
    padding: 20,
  },
  groupContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    marginTop: 4,
  },
  label: {
    marginTop: 10,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  inputDropdownLabel: {
    marginTop: 10,
    color: '#000',
  },
  inputDropdown: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    marginVertical: 10,
    justifyContent: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    marginVertical: 10,
  },
  center: {
    textAlign: 'center',
  },
  sideBySide: {
    width: '45%',
  },
  inputNotes: {
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 5,
    marginVertical: 10,
    minHeight: 100,
    paddingTop: 10,
  },
  btnDate: {
    marginTop: 12,
  },
  btnSideBySide: {
    width: '48%',
  },
  btn: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  archive: {
    marginTop: 10,
    marginBottom: 15,
    width: '100%',
    padding: 12,
    borderRadius: 25,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  previewPhoto: {
    alignItems: 'center',
    marginVertical: 10,
    position: 'relative',
  },
  deleteButton: {
    position: 'absolute',
    top: 0,
    right: 50,
    padding: 2,
    borderRadius: 50,
    borderWidth: 2,
  },
  validationError: {
    marginTop: 5,
    marginLeft: 10,
    color: '#ff4444',
    fontWeight: 'bold',
    fontSize: 12,
  },
  datePickerContainer: {
    width: 130,
  },
  dateTimePickerContainer: {
    width: Dimensions.get('window').width / 1.75,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AlertScreen;
