import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { RootStackParamList } from '../types';
import { useApi } from '../context/ApiContext';
import IconDataList from '../common/IconDataList';
import Center from '../components/Center';
import ErrorMessage from '../components/ErrorMessage';
import { useTheme } from '../context/ThemeContext';
import { useRefresh } from '../context/RefreshContex';
import { AlertModel } from '../common/AlertModel';

type ItemProps = {
  item: AlertModel;
  onPress: () => void;
  backgroundColor: string;
  textColor: string;
};

type DocumentDisplayScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Document'
>;

type Props = {
  navigation: DocumentDisplayScreenNavigationProp;
  route: any;
};

const DocumentsScreen: React.FC<Props> = ({ navigation, route }) => {
  const [selectedId, setSelectedId] = useState<string | undefined>();
  const [alerts, setAlerts] = useState<AlertModel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);

  const iconDataList = new IconDataList();
  const [icons, setIcons] = useState<any>();

  const { themeStyles } = useTheme();
  const { documents, clearDocuments } = useRefresh();
  const { getAlertsWithDocuments } = useApi();

  useEffect(() => {
    setIcons({ fileIcon: iconDataList.getIconByName('file') });
    setIsLoading(true);
    fetchData();
  }, []);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      setIsLoading(true);
      fetchData();
    });

    return unsubscribe;
  }, [navigation]);

  // useEffect(() => {

  //   if (documents) {
  //     setIsLoading(true);
  //     fetchData(); 
  //     clearDocuments();
  //   }
  // }, [documents]);

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  const fetchData = () => {


    getAlertsWithDocuments()
      .then(result => {
        setAlerts([...result]);
        setIsError(false);
      })
      .catch(error => {
        console.log(error);
        setIsError(true);
      })
      .finally(() => {
        setIsLoading(false);
        setRefreshing(false);
      });
  };

  const renderItem = ({ item }: { item: AlertModel }) => {
    const windowWidth = Dimensions.get('window').width;
    const inputIconSize = 32;

    const displayDocument = async (item: AlertModel) => {
      navigation.navigate('Document', { alertId: item.id });
    };

    const Item = ({ item, onPress, backgroundColor, textColor }: ItemProps) => (
      <TouchableOpacity
        onPress={onPress}
        style={[styles.item, { backgroundColor }]}>
        <View style={{ flexDirection: 'row' }}>
          <View
            style={{
              borderColor: '#ccc',
              borderRightWidth: 2,
              paddingRight: 10,
              justifyContent: 'center',
            }}>
            {icons.fileIcon != null && (
              <icons.fileIcon
                width={inputIconSize}
                height={inputIconSize}
                fill={themeStyles.primary}
              />
            )}
          </View>
          <View style={{ marginLeft: 10, width: '90%' }}>
            <Text style={[styles.title, { color: themeStyles.primary }]}>
              {item.categoryName}
            </Text>
            <Text style={[styles.description, { color: themeStyles.text }]}>
              {item.companyName}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );

    return (
      <Item
        item={item}
        onPress={() => displayDocument(item)}
        backgroundColor={themeStyles.background}
        textColor={themeStyles.primary}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={[styles.content, { backgroundColor: themeStyles.background }]}>
        {!isLoading ? (
          !isError ? (
            alerts?.length > 0 ? (
              <FlatList
                data={alerts}
                renderItem={renderItem}
                keyExtractor={item => item.id}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                  />
                }
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <ScrollView
                contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                  />
                }
                showsVerticalScrollIndicator={false}>
                <Center>
                  <Text
                    style={{
                      textAlignVertical: 'center',
                      textAlign: 'center',
                      color: themeStyles.text,
                    }}>
                    You have no documents to show here, they can be added in
                    your alerts.
                  </Text>
                </Center>
              </ScrollView>
            )
          ) : (
            <ErrorMessage onRefresh={onRefresh} refreshing={refreshing} />
          )
        ) : (
          <Center>
            <ActivityIndicator color={themeStyles.primary} size="large" />
          </Center>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  heading: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  item: {
    padding: 20,
    marginVertical: 5,
    marginHorizontal: 2,
    borderColor: '#767676',
    borderWidth: 1,
    borderRadius: 10,
  },
  title: {
    fontSize: 18,
    color: '#108a00',
  },
  description: {
    fontSize: 16,
  },
});

export default DocumentsScreen;
