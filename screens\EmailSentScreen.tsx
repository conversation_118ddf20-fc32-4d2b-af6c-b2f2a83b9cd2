import React, { useState } from 'react';
import { View, Text, Button, TouchableWithoutFeedback, StyleSheet, TextInput, TouchableOpacity, Keyboard, Dimensions } from 'react-native';
import { RootStackParamList } from '../types';
import { StackNavigationProp } from '@react-navigation/stack';
import EmailSentIcon from '../assets/images/email-sent.svg';
import { useRoute } from '@react-navigation/native';
import { useTheme } from '../context/ThemeContext';

type ForgotPasswordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ForgotPassword'>;

type Props = {
  navigation: ForgotPasswordScreenNavigationProp;
};

type RouteParams = {
  fromScreen?: string;
};

const EmailSentScreen : React.FC<Props> = ({navigation}) => {
    const { themeStyles } = useTheme();

    const windowWidth = Dimensions.get('window').width;
    const inputIconSize = windowWidth * 0.4;

    const route = useRoute();
    const { fromScreen }: RouteParams = route.params || {};

    const close = () => {
      
      if(fromScreen == 'forgotPassword')
      {
        navigation.navigate("Login");
      }
      else if (fromScreen == "expert")
      {
        navigation.navigate("Home", { refresh: true});
      }
    }
   
    return (
        <View style={[styles.container, { backgroundColor: themeStyles.background}]}>
            <EmailSentIcon width={inputIconSize} height={inputIconSize} />
            <Text style={[styles.label, { color: themeStyles.text}]}>Email has been sent</Text>

            <View style={styles.btnContainer}>
              <TouchableOpacity style={styles.btn} onPress={close}>
                  <Text style={[styles.btnText, { color: themeStyles.text}]}>Close</Text>
              </TouchableOpacity>
            </View>
            
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
      backgroundColor:'#fff'
    },
    label:{
      marginTop:20
    },
    btnContainer:{
      width:'100%',
      marginTop:20,
      alignItems: 'center',
    },
    btn: {
      marginTop: 10,
      marginBottom:15,
      width:'80%',
      borderColor:'#c5c5c5',
      borderWidth:2,
      padding:12,
      borderRadius:25,
      height:45
    },
    btnText : {
      color:'#767676',
      fontWeight:"bold",
      textAlign:"center"
    },
    
  });

export default EmailSentScreen;