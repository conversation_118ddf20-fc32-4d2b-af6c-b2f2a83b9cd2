import { useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState } from 'react';
import { Text, StyleSheet, TouchableWithoutFeedback, Keyboard, View, TextInput, TouchableOpacity, Dimensions, FlatList, ActivityIndicator } from 'react-native';
import { useApi } from '../context/ApiContext';
import { RootStackParamList } from '../types';
import IconDataList from '../common/IconDataList';
import IconDataItem from '../common/IconDataItem';
import { FieldValidation, ValidateForm, ValidateField } from '../common/Validation';
import { useTheme } from '../context/ThemeContext';

type SubCategoryScreenNavigationProp = StackNavigationProp<RootStackParamList, 'SubCategory'>;

type Props = {
    navigation: SubCategoryScreenNavigationProp;
};

type RouteParams = {
    categoryId?: string;
};

const SubCategoryScreen: React.FC<Props> = ({ navigation }) => {

    const route = useRoute();
    const { categoryId }: RouteParams = route.params || {};
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const { themeStyles } = useTheme();
    const { createCategory } = useApi();

    const windowWidth = Dimensions.get('window').width;
    const inputIconSize = 32;
    const numColumns = 4;
    const iconDataList = new IconDataList();

    const [form, setForm] = useState({ name: '', description: '', selectedIcon: '' });
    const [validation, setValidation] = useState({ name: '', description: '' });
    const [isFormValid, setIsFormValid] = useState<boolean>(false);

    const validationSchema: FieldValidation[] = [{
        name: "name",
        requirements: ["Required"],
        displayName: "Name"
    }, {
        name: "description",
        requirements: ["Required"],
        displayName: "Description"
    }, {
        name: "selectedIcon",
        requirements: ["Required"],
        displayName: "Icon"
    }]

    const onValueChange = (name: string, value: any) => {
        let newForm = { ...form, [name]: value };
        setForm(newForm);
        setIsFormValid(ValidateForm(newForm, validationSchema));
    }

    const onFieldBlur = (name: string) => {
        let schema = validationSchema.filter(x => x.name == name);

        if (schema.length > 0) {
            //@ts-ignore
            let error = ValidateField(form, schema[0]);
            setValidation({ ...validation, [name]: error });
        }
    }

    const createSubCategory = () => {
        if (categoryId) {
            setIsLoading(true);

            createCategory({ categoryId: categoryId, name: form.name, description: form.description, iconName: form.selectedIcon })
                .then(() => {
                    navigation.navigate("SubCategories", { categoryId: categoryId });
                })
                .catch(() => {

                })
                .finally(() => {
                    setIsLoading(false);
                })
        }
    }

    const dismissKeyboard = () => {
        Keyboard.dismiss();
    };

    const iconData = iconDataList.getIconData();

    const renderItem = ({ item }: { item: IconDataItem }) => (
        <TouchableOpacity style={[styles.iconButton, { backgroundColor: themeStyles.unselected }]} onPress={() => handleIconSelection(item)}>
            <item.icon width={inputIconSize} height={inputIconSize} fill={form.selectedIcon == item.name ? themeStyles.primary : themeStyles.text } />
        </TouchableOpacity>
    );

    const handleIconSelection = (selectedIcon: IconDataItem) => {
        onValueChange("selectedIcon", selectedIcon.name);
    };

    return (
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
            <View style={[styles.container, { backgroundColor: themeStyles.background}]}>
                <Text style={[styles.label, { color: themeStyles.text }]}>Name</Text>
                <TextInput
                    style={[styles.input, { color: themeStyles.text, backgroundColor: themeStyles.background }]}
                    onChangeText={(text) => onValueChange("name", text)}
                    value={form.name}
                    onBlur={() => onFieldBlur("name")}
                />
                {validation.name != "" && (
                    <Text style={styles.validationError}>{validation.name}</Text>
                )}
                <Text style={[styles.label, { color: themeStyles.text }]}>Description</Text>
                <TextInput
                    style={[styles.input, { color: themeStyles.text, backgroundColor: themeStyles.background }]}
                    onChangeText={(text) => onValueChange("description", text)}
                    value={form.description}
                    onBlur={() => onFieldBlur("description")}
                />
                {validation.description != "" && (
                    <Text style={styles.validationError}>{validation.description}</Text>
                )}
                <FlatList
                    data={iconData}
                    renderItem={renderItem}
                    keyExtractor={(item) => item.id.toString()}
                    numColumns={numColumns}
                    contentContainerStyle={styles.iconGrid}
                />
                <TouchableOpacity style={isFormValid ? [styles.btn, { backgroundColor: themeStyles.primary }] : [styles.btnDisabled, { backgroundColor: themeStyles.primaryDisabled }]} onPress={createSubCategory} disabled={!isFormValid}>
                    {!isLoading ? (
                        <Text style={[styles.btnText, { color: themeStyles.background }]}>Add</Text>
                    ) : (
                        <ActivityIndicator color={themeStyles.background} />
                    )}
                </TouchableOpacity>
            </View>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: '#fff'
    },
    label: {
        color: '#767676',
        zIndex: -1
    },
    input: {
        width: '100%',
        height: 40,
        borderColor: '#ccc',
        borderWidth: 1,
        marginBottom: 10,
        paddingHorizontal: 10,
        color: '#000',
        backgroundColor: '#fff',
        borderRadius: 25,
        marginTop: 20,
    },
    btn: {
        marginTop: 20,
        marginBottom: 15,
        width: '100%',
        backgroundColor: '#108a00',
        padding: 12,
        borderRadius: 25,
        height: 45
    },
    btnDisabled: {
        marginTop: 20,
        marginBottom: 15,
        width: '100%',
        backgroundColor: '#ccc',
        padding: 12,
        borderRadius: 25,
        height: 45
    },
    btnText: {
        color: '#fff',
        fontWeight: "bold",
        textAlign: "center"
    },
    iconGrid: {
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 20
    },
    iconButton: {
        justifyContent: 'center',
        alignItems: 'center',
        margin: 5,
        width: 60,
        height: 60,
        borderRadius: 10,
        backgroundColor: '#f2f2f2',
    },
    validationError: {
        marginTop: 5,
        marginLeft: 10,
        color: "#767676",
        fontWeight: "bold"
    }
});

export default SubCategoryScreen;