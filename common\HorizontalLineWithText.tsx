import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface HorizontalLineWithTextProps {
    text: string; 
}

const HorizontalLineWithText = ({ text }: HorizontalLineWithTextProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.line} />
      <Text style={styles.text}>{text}</Text>
      <View style={styles.line} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  line: {
    flex: 1,
    height: 1,
    backgroundColor: '#c5c5c5',
  },
  text: {
    marginHorizontal: 10,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default HorizontalLineWithText;
