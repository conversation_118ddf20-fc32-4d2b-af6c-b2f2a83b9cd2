import React, { useState, useEffect, useRef } from 'react';
import { Modal, View, StyleSheet } from 'react-native';
import { useApi } from '../../context/ApiContext';
import { useRefresh } from '../../context/RefreshContex';
import ConfirmModal from '../ConfirmModal';
import AlertModalOptions from './AlertModalOptions';
import AlertModelSnooze from './AlertModalSnooze';

export default function AlertModal({
  navigation,
  visible,
  alert: initialAlert,
  alertCompleted,
  onAlertActioned,
  bypassTimeCheck = false,
}: {
  navigation: any;
  visible: boolean;
  alert: any;
  alertCompleted: () => void;
  onAlertActioned: () => void;
  bypassTimeCheck?: boolean;
}) {
  const { refreshScreens } = useRefresh();
  const { archiveAlert, deleteAlert,updateAlert } = useApi();
  const intervalRef = useRef<NodeJS.Timeout | number | null>(null);
  const [alertState, setAlertState] = useState<any>(initialAlert);
  const [screen, setScreen] = useState<number>(0);
  const [shouldShow, setShouldShow] = useState<boolean>(false);

  useEffect(() => {
    if (initialAlert && Object.keys(initialAlert).length > 1) {
      setAlertState({ ...initialAlert });
    }
  }, [initialAlert]);

  useEffect(() => {
    if (bypassTimeCheck) {
      // For pending notifications, show immediately without time check
      setShouldShow(true);
    } else if (alertState?.nextAlertDate) {
      startTimeCheck(alertState.nextAlertDate);
    }
  }, [alertState?.nextAlertDate, bypassTimeCheck]);

  const onAlertCompleted = () => {
    setScreen(0);
    setShouldShow(false);
    alertCompleted();
  };
  const startTimeCheck = (nextAlertDate: string) => {
    if (intervalRef.current !== null) {
      clearInterval(intervalRef.current);
    }

    const checkTime = () => {
      const currentTime = new Date();
      const alertTime = new Date(nextAlertDate);

      const isSameDay = currentTime.toDateString() === alertTime.toDateString();
      const isMatchingTime =
        isSameDay &&
        currentTime.getHours() === alertTime.getHours() &&
        currentTime.getMinutes() === alertTime.getMinutes();

      setShouldShow(isMatchingTime);

      if (isMatchingTime) {
        setShouldShow(true);
        setScreen(0);
        clearInterval(intervalRef.current as number);
      }
    };

    checkTime();
    intervalRef.current = setInterval(checkTime, 1000) as unknown as number;
  };

  const handleAlertUpdate = (updatedAlertData: any) => {
    setAlertState((prevAlert: any) => {
      if (!updatedAlertData || Object.keys(updatedAlertData).length === 1) {
        return prevAlert;
      }
      return {
        ...prevAlert,
        ...updatedAlertData,
        nextAlertDate: updatedAlertData.nextAlertDate || prevAlert.nextAlertDate,
      };
    });

    setShouldShow(false);
    startTimeCheck(updatedAlertData.nextAlertDate);
  };


  useEffect(() => {
    return () => {
      if (intervalRef.current !== null) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <Modal visible={shouldShow} transparent animationType="fade">
        <View style={styles.container}>

          {screen === 0 && (
            <AlertModalOptions
              navigation={navigation}
              alert={alertState}
              onSelectScreen={setScreen}
              alertCompleted={alertCompleted}
              onAlertActioned={onAlertActioned}
              bypassTimeCheck={bypassTimeCheck}
            />
          )}
          {screen === 1 && (
            <ConfirmModal
              visible={true}
              onConfirm={() => {
                archiveAlert(alertState.id)
                  .then(() => refreshScreens(['Home', 'Documents']))
                  .finally(() => {
                    alertCompleted();
                    setShouldShow(false);
                  });
              }}
              onReject={() => {
                deleteAlert(alertState.id)
                  .then(() => refreshScreens(['Home', 'Documents']))
                  .finally(() => {
                    alertCompleted();
                    setShouldShow(false);
                  });
              }}
              question="Do you want to archive this alert?"
              onBack={() => setScreen(0)}
            />
          )}
          {screen === 2 && (
            <AlertModelSnooze
              alert={alertState}
              alertCompleted={alertCompleted}
              onBack={() => setScreen(0)}
              onAlertUpdate={handleAlertUpdate}
            />
          )}
        </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
      container: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
      },
  modal: {
    padding: 20,
    borderRadius: 8,
    width: '90%',
  },
  questionText: {
    fontSize: 16,
  },
  buttonsContainer: {
    marginTop: 15,
    flexDirection: 'column',
    justifyContent: 'space-evenly',
  },
  button: {
    width: '100%',
    padding: 10,
    borderRadius: 25,
    height: 40,
    marginBottom: 10,
  },
  confirmText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  reject: {
    borderWidth: 2,
  },
  rejectText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -2,
  },
});
