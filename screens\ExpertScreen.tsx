import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Keyboard,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { CategoryType } from '../api/types/CategoryType';
import CustomDropdown from '../common/CustomDropdown';
import { Option } from '../common/option';
import {
  FieldValidation,
  ValidateField,
  ValidateForm,
} from '../common/Validation';
import { useApi } from '../context/ApiContext';
import { useRefresh } from '../context/RefreshContex';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

export interface QuestionType {
  categoryId: string;
  categoryName: string;
  phoneNumber: string;
  preferredContactTime: string;
  preferredContactTimeName: string;
  question: string;
}

type EmailSentScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'EmailSent'
>;

type Props = {
  navigation: EmailSentScreenNavigationProp;
};

interface RouteParams {
  categoryId?: string;
  subCategoryId?: string;
}

const preferredContactTimes: Option[] = [
  {
    Id: '0',
    value: 'Midweek day time 9am - 5pm',
  },
  {
    Id: '1',
    value: 'Midweek evening 5pm - 9pm',
  },
  {
    Id: '2',
    value: 'Weekend',
  },
];

const ExpertScreen: React.FC<Props> = ({navigation}) => {
  const route = useRoute();
  const {categoryId}: RouteParams = route.params || {};

  const {themeStyles, theme} = useTheme();
  const {refreshScreens} = useRefresh();
  const {createQuestion, getCategories} = useApi();

  const [showCategoryList, setShowCategoryList] = useState(false);
  const [isEditable, setIsEditable] = useState(true);
  const [showPreferredContactTimeList, setShowPreferredContactTimeList] =
    useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<Option[]>([]);

  const [form, setForm] = useState<QuestionType>({
    categoryId: '',
    categoryName: '',
    phoneNumber: '',
    preferredContactTime: '',
    preferredContactTimeName: '',
    question: '',
  });
  const [validation, setValidation] = useState({
    categoryId: '',
    question: '',
    preferredContactTimes: '',
    phoneNumber: '',
  });
  const [isFormValid, setIsFormValid] = useState<boolean>(false);

  const validationSchema: FieldValidation[] = [
    {
      name: 'categoryId',
      requirements: ['Required'],
      displayName: 'Category',
    },
    {
      name: 'preferredContactTime',
      requirements: ['Required'],
      displayName: 'Preferred contact time',
    },
    {
      name: 'phoneNumber',
      requirements: ['Required'],
      displayName: 'Phone number',
    },
    {
      name: 'question',
      requirements: ['Required'],
      displayName: 'Question',
    },
  ];

  const onValueChange = (name: string, value: any) => {
    let newForm: QuestionType;

    if (name === 'categoryId') {
      newForm = {
        ...form,
        categoryId: value.Id,
        categoryName: value.value,
      };

      showCategoryList ? setShowCategoryList(false) : setShowCategoryList(true);
    } else if (name === 'preferredContactTime') {
      newForm = {
        ...form,
        preferredContactTime: value.Id,
        preferredContactTimeName: value.value,
      };

      setShowPreferredContactTimeList(false);
    } else {
      newForm = {...form, [name]: value};
    }
    setForm(newForm);
    setIsFormValid(ValidateForm(newForm, validationSchema));
  };

  const onFieldBlur = (name: string) => {
    let schema = validationSchema.filter(x => x.name === name);

    if (schema.length > 0) {
      //@ts-ignore
      let error = ValidateField(form, schema[0]);
      setValidation({...validation, [name]: error});
    }
  };

  useEffect(() => {
    setForm({
      ...form,
      categoryId: categoryId ? categoryId : '',
    });
    fetchCategories();
    fetchPhoneNumber();
  }, []);

  useEffect(() => {
    if (form.categoryId) {
      var index = categories
        .map(category => {
          return category.Id;
        })
        .indexOf(form.categoryId);

      if (index > -1) {
        setForm({...form, categoryName: categories[index].value});
      }
    }
  }, [categories]);

  const fetchCategories = () => {
    getCategories(true)
      .then(result => {
        let options: Option[] = result.map((category: CategoryType) => {
          return {Id: category.categoryId, value: category.name};
        });
        setCategories(options);
      })
      .catch(error => {
        console.log('Error getting categories ', error);
      });
  };
  const fetchPhoneNumber = async () => {
    try {
      const phoneNumber = await AsyncStorage.getItem('userPhoneNumber');
      if (phoneNumber) {
        setIsEditable(false);
        setForm(prevForm => ({
          ...prevForm,
          phoneNumber,
        }));
      }
    } catch (error) {
      console.log('Error fetching phone number:', error);
    }
  };
  const onSend = () => {
    setIsLoading(true);
    createQuestion(form)
      .then(() => {
        navigation.navigate('EmailSent', {fromScreen: 'expert'});
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
        refreshScreens(['Home']);
      });
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <View
      style={[styles.background, {backgroundColor: themeStyles.background}]}>
      <ScrollView
        style={styles.scrollViewStyle}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag">
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.expert}>
            <Text style={[styles.heading, {color: themeStyles.text}]}>
              Some categories are worth seeking guidance or educating yourself
              on. For a free quote please fill in
            </Text>
            <View style={styles.categoryDropdown}>
              <Text style={[styles.label, {color: themeStyles.text}]}>
                Category <Text style={styles.mandatoryStar}>*</Text>
              </Text>
              <CustomDropdown
                placeholderText="Category"
                textColor={themeStyles.inputText}
                options={categories}
                background={themeStyles.background}
                onSelectOption={selectedCategory =>
                  onValueChange('categoryId', selectedCategory)
                }
                style={styles.input}
              />
            </View>
            <View style={styles.preferredContactTimeDropdown}>
              <Text style={[styles.label, {color: themeStyles.text}]}>
                Preferred Contact Time{' '}
                <Text style={styles.mandatoryStar}>*</Text>
              </Text>
              <CustomDropdown
                placeholderText="Preferred Contact Time"
                textColor={themeStyles.inputText}
                background={themeStyles.background}
                options={preferredContactTimes}
                onSelectOption={selectedTime =>
                  onValueChange('preferredContactTime', selectedTime)
                }
                style={[styles.input]}
              />
            </View>
            <Text style={[styles.label, {color: themeStyles.text}]}>
              Phone number <Text style={styles.mandatoryStar}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: themeStyles.text,
                  backgroundColor: themeStyles.background,
                },
              ]}
              onChangeText={value => onValueChange('phoneNumber', value)}
              value={form.phoneNumber}
              placeholder="i.e. 8974561320"
              placeholderTextColor={theme === 'dark' ? '#999' : '#999'}
              keyboardType="phone-pad"
              maxLength={15}
            />
            {validation.phoneNumber !== '' && (
              <Text style={styles.validationError}>
                {validation.phoneNumber}
              </Text>
            )}
            <Text style={[styles.label, {color: themeStyles.text}]}>
              Question <Text style={styles.mandatoryStar}>*</Text>
            </Text>
            <View style={styles.questionInputContainer}>
              <TextInput
                style={[
                  styles.inputQuestion,
                  {
                    color: themeStyles.text,
                    backgroundColor: themeStyles.background,
                  },
                ]}
                textAlign="left"
                onChangeText={value => onValueChange('question', value)}
                value={form.question}
                placeholder={
                  form.question ? '' : 'i.e. How does the internet work?'
                }
                placeholderTextColor={theme === 'dark' ? '#fff' : 'gray'}
                multiline
                numberOfLines={5}
                textAlignVertical="top"
                onBlur={() => onFieldBlur('question')}
              />
            </View>
            {validation.question !== '' && (
              <Text style={styles.validationError}>{validation.question}</Text>
            )}
            <TouchableOpacity
              style={[
                styles.buttonContainer,
                isFormValid
                  ? {backgroundColor: themeStyles.primary}
                  : {backgroundColor: themeStyles.primaryDisabled},
              ]}
              onPress={onSend}
              disabled={!isFormValid}>
              {!isLoading ? (
                <Text style={[styles.btnText, {color: themeStyles.background}]}>
                  Send
                </Text>
              ) : (
                <ActivityIndicator color={themeStyles.background} />
              )}
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollViewStyle: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 10,
    paddingTop: 20,
  },
  expert: {
    padding: 10,
  },
  heading: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  label: {
    marginVertical: 8,
    marginHorizontal: 5,
    color: '#767676',
    zIndex: -1,
    fontWeight: '500',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 15,
    // color: 'rgba(0, 0, 0, 0.6)',
    // backgroundColor: '#fff',
    borderRadius: 25,
    zIndex: -1,
    textAlign: 'left',
    fontWeight: '500',
    textAlignVertical: 'center',
    paddingTop: 0,
    paddingBottom: 0,
    marginBottom: 10,
  },
  phoneInput: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 15,
    color: 'rgba(0, 0, 0, 0.6)',
    backgroundColor: '#fff',
    borderRadius: 25,
    zIndex: -1,
    textAlign: 'left',
    fontWeight: '500',
    textAlignVertical: 'center',
    paddingTop: 0,
    paddingBottom: 0,
  },
  questionInputContainer: {
    width: '100%',
    marginBottom: 10,
  },
  inputQuestion: {
    height: 100,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 15,
    color: 'rgba(0, 0, 0, 0.6)',
    backgroundColor: '#fff',
    borderRadius: 25,
    minHeight: 100,
    zIndex: -1,
    textAlign: 'left',
    textAlignVertical: 'top',
    paddingTop: 12,
    paddingBottom: 10,
  },
  btn: {
    marginTop: 0,
    marginBottom: 0,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 50,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  btnDisabled: {
    marginTop: 0,
    marginBottom: 0,
    width: '100%',
    backgroundColor: '#ccc',
    padding: 12,
    borderRadius: 25,
    height: 50,
  },
  buttonContainer: {
    marginTop: 60,
    marginBottom: 10,
    width: '100%',
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
    letterSpacing: 0.5,
  },
  categoryDropdown: {
    zIndex: 2,
  },
  preferredContactTimeDropdown: {
    zIndex: 1,
  },
  validationError: {
    marginTop: 5,
    marginLeft: 10,
    color: '#767676',
    fontWeight: 'bold',
  },
  mandatoryStar: {
    color: 'red',
    fontWeight: 'bold',
  },
});

export default ExpertScreen;
