import AsyncStorage from '@react-native-async-storage/async-storage';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import {
  NavigationProp,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {
  CopilotProvider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { BottomFabBar } from 'rn-wave-bottom-bar';
import PhoneIcon from '../assets/images/customer-service-svgrepo-com.svg';
import DocumentIcon from '../assets/images/file-solid.svg';
import GearIcon from '../assets/images/gear-solid.svg';
import HomeIcon from '../assets/images/house-solid.svg';
import ListIcon from '../assets/images/list-solid.svg';
import { useApi } from '../context/ApiContext';
import { useTheme } from '../context/ThemeContext';
import CategoriesScreen from '../screens/CategoriesScreen';
import DocumentsScreen from '../screens/DocumentsScreen';
import HomeScreen from '../screens/HomeScreen';
import SettingsScreen from '../screens/SettingsScreen';
import { RootStackParamList } from '../types';

const Tab = createBottomTabNavigator();
const WalkthroughableView = walkthroughable(View);

type LoginScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Login'
>;
type Navigation = NavigationProp<RootStackParamList>;

interface Props {
  navigation: LoginScreenNavigationProp;
}

interface WindowDimensions {
  width: number;
  height: number;
}

// Constants
const ICON_SIZE = 32;
const ONBOARDING_DELAY = 500;
const ONBOARDING_STORAGE_KEY = 'onboardingComplete';
const USER_PHONE_STORAGE_KEY = 'userPhoneNumber';

// Custom hooks
const useWindowDimensions = (): WindowDimensions => {
  const [windowDimensions, setWindowDimensions] = useState(() => ({
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  }));

  useEffect(() => {
    const handleResize = ({window}: {window: WindowDimensions}) => {
      setWindowDimensions(window);
    };

    const subscription = Dimensions.addEventListener('change', handleResize);
    return () => subscription?.remove();
  }, []);

  return windowDimensions;
};

const useOnboarding = () => {
  const [onboardingComplete, setOnboardingComplete] = useState(false);
  const [isCopilotStarted, setIsCopilotStarted] = useState(false);
  const {start, copilotEvents} = useCopilot();

  useEffect(() => {
    const checkOnboarding = async () => {
      try {
        const onboardingStatus = await AsyncStorage.getItem(
          ONBOARDING_STORAGE_KEY,
        );
        const isComplete = !!onboardingStatus;
        setOnboardingComplete(isComplete);

        if (!isComplete && !isCopilotStarted) {
          setTimeout(() => {
            start();
            setIsCopilotStarted(true);
          }, ONBOARDING_DELAY);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
      }
    };

    checkOnboarding();
  }, [start, isCopilotStarted]);

  useEffect(() => {
    if (!isCopilotStarted || !copilotEvents) return;

    const handleStop = async () => {
      try {
        await AsyncStorage.setItem(ONBOARDING_STORAGE_KEY, 'true');
        setOnboardingComplete(true);
      } catch (error) {
        console.error('Error saving onboarding status:', error);
      }
    };

    copilotEvents.on('stop', handleStop);
    return () => copilotEvents.off('stop', handleStop);
  }, [copilotEvents, isCopilotStarted]);

  return {onboardingComplete, isCopilotStarted};
};

// Memoized components
const TabIcon = React.memo<{
  IconComponent: React.ComponentType<any>;
  stepProps: {
    text: string;
    order: number;
    name: string;
  };
  customBoxHeight: number;
  customBoxWidth: number;
  translateYDynamic: number;
  fillColor: string;
}>(
  ({
    IconComponent,
    stepProps,
    customBoxHeight,
    customBoxWidth,
    translateYDynamic,
    fillColor,
  }) => (
    <CopilotStep {...stepProps}>
      <WalkthroughableView
        style={[
          styles.customStepBox,
          {height: customBoxHeight, width: customBoxWidth},
          {transform: [{translateY: translateYDynamic}]},
        ]}>
        <IconComponent
          width={ICON_SIZE}
          height={ICON_SIZE}
          fill={fillColor}
          style={[
            styles.raised,
            {transform: [{translateY: -translateYDynamic}]},
          ]}
        />
      </WalkthroughableView>
    </CopilotStep>
  ),
);

const HeaderLogo = React.memo(() => (
  <Image
    source={require('../assets/images/logo-dark.png')}
    style={styles.headerLogo}
  />
));

const UserInitialsButton = React.memo<{
  userInitials: string;
  onPress: () => void;
  themeStyles: any;
}>(({userInitials, onPress, themeStyles}) => (
  <TouchableOpacity onPress={onPress} style={styles.userInitialsContainer}>
    <Text style={[styles.userInitialsText, {color: themeStyles.primary}]}>
      {userInitials}
    </Text>
  </TouchableOpacity>
));

const ExpertButton = React.memo<{
  onPress: () => void;
  themeStyles: any;
  windowWidth: number;
  translateYDynamic: number;
}>(({onPress, themeStyles, windowWidth, translateYDynamic}) => (
  <TouchableOpacity style={styles.expertButtonContainer} onPress={onPress}>
    <CopilotStep
      text="Tap here to ask an expert for help!"
      order={1}
      name="expertTab">
      <WalkthroughableView
        style={[
          styles.customStepBox,
          {
            height: 30,
            width: windowWidth * 0.13,
            marginRight: -10,
          },
          {transform: [{translateY: translateYDynamic}]},
        ]}>
        <PhoneIcon
          width={27}
          height={27}
          fill={themeStyles.primary}
          style={[
            styles.raised,
            {transform: [{translateY: -translateYDynamic}]},
          ]}
        />
      </WalkthroughableView>
    </CopilotStep>
  </TouchableOpacity>
));

const TabNavigationWithTooltip = () => {
  const {themeStyles, theme} = useTheme();
  const {getUserSettings} = useApi();
  const navigation = useNavigation<Navigation>();
  const windowDimensions = useWindowDimensions();
  const {onboardingComplete, isCopilotStarted} = useOnboarding();

  const [userInitials, setUserInitials] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Memoized calculations
  const dimensions = useMemo(
    () => ({
      customBoxHeight: windowDimensions.height * 0.07,
      customBoxWidth: windowDimensions.width * 0.2,
      translateYDynamic:
        Platform.OS === 'ios' ? 0 : windowDimensions.height * 0.05,
      headerHeight:
        Platform.OS === 'ios' ? 100 : windowDimensions.height * 0.08,
    }),
    [windowDimensions],
  );

  const iconFillColor = useMemo(
    () => (theme === 'dark' ? themeStyles.color : themeStyles.primary),
    [theme, themeStyles],
  );

  // Fetch user settings
  const fetchUserSettings = useCallback(async () => {
    try {
      const result = await getUserSettings();
      if (result?.firstName && result?.surname) {
        const initials = (
          result.firstName[0] + result.surname[0]
        ).toUpperCase();
        setUserInitials(initials);
      }

      if (result?.phoneNo) {
        await AsyncStorage.setItem(USER_PHONE_STORAGE_KEY, result.phoneNo);
      }
    } catch (error) {
      console.error('Error fetching user settings:', error);
      setUserInitials('U'); // Default initials
    } finally {
      setIsLoading(false);
    }
  }, [getUserSettings]);

  // Initial load
  useEffect(() => {
    fetchUserSettings();
  }, [fetchUserSettings]);

  // Refresh on focus
  useFocusEffect(
    useCallback(() => {
      if (!isLoading) {
        fetchUserSettings();
      }
    }, [fetchUserSettings, isLoading]),
  );

  // Navigation handlers
  const navigateToProfile = useCallback(() => {
    navigation.navigate('Profile');
  }, [navigation]);

  const navigateToExpert = useCallback(() => {
    navigation.navigate('Expert');
  }, [navigation]);

  // Screen options with memoization
  const screenOptions = useMemo(
    () => ({
      headerStyle: {
        backgroundColor: themeStyles.background,
        height: dimensions.headerHeight,
        shadowColor: '#000',
      },
      headerTintColor: themeStyles.primary,
      headerTitleAlign: 'center' as const,
      tabBarActiveTintColor: themeStyles.white,
      tabBarActiveBackgroundColor: themeStyles.white,
      tabBarInactiveBackgroundColor: 'red',
    }),
    [themeStyles, dimensions.headerHeight],
  );

  // Tab bar component
  const tabBarComponent = useCallback(
    (props: any) => (
      <BottomFabBar
        mode="square"
        isRtl={false}
        focusedButtonStyle={styles.focusedButtonStyle}
        bottomBarContainerStyle={{backgroundColor: themeStyles.background}}
        {...props}
      />
    ),
    [themeStyles.background],
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={themeStyles.primary} />
      </View>
    );
  }

  return (
    <Tab.Navigator screenOptions={screenOptions} tabBar={tabBarComponent}>
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: () => (
            <TabIcon
              IconComponent={HomeIcon}
              stepProps={{
                text: 'This is your home tab!',
                order: 2,
                name: 'homeTab',
              }}
              customBoxHeight={windowDimensions.height * 0.08}
              customBoxWidth={windowDimensions.width * 0.18}
              translateYDynamic={dimensions.translateYDynamic}
              fillColor={iconFillColor}
            />
          ),
          headerLeft: () => (
            <UserInitialsButton
              userInitials={userInitials}
              onPress={navigateToProfile}
              themeStyles={themeStyles}
            />
          ),
          headerRight: () => (
            <ExpertButton
              onPress={navigateToExpert}
              themeStyles={themeStyles}
              windowWidth={windowDimensions.width}
              translateYDynamic={dimensions.translateYDynamic}
            />
          ),
          headerTitle: HeaderLogo,
        }}
      />

      <Tab.Screen
        name="Documents"
        component={DocumentsScreen}
        options={{
          tabBarIcon: () => (
            <TabIcon
              IconComponent={DocumentIcon}
              stepProps={{
                text: 'Here you can manage your documents!',
                order: 3,
                name: 'documentsTab',
              }}
              customBoxHeight={dimensions.customBoxHeight}
              customBoxWidth={dimensions.customBoxWidth}
              translateYDynamic={dimensions.translateYDynamic}
              fillColor={iconFillColor}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{
          tabBarIcon: () => (
            <TabIcon
              IconComponent={ListIcon}
              stepProps={{
                text: 'Browse your categories here!',
                order: 4,
                name: 'categoriesTab',
              }}
              customBoxHeight={dimensions.customBoxHeight}
              customBoxWidth={dimensions.customBoxWidth}
              translateYDynamic={dimensions.translateYDynamic}
              fillColor={iconFillColor}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          tabBarIcon: () => (
            <TabIcon
              IconComponent={GearIcon}
              stepProps={{
                text: 'Adjust your settings here!',
                order: 5,
                name: 'settingsTab',
              }}
              customBoxHeight={dimensions.customBoxHeight}
              customBoxWidth={dimensions.customBoxWidth}
              translateYDynamic={dimensions.translateYDynamic}
              fillColor={iconFillColor}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  raised: {
    shadowColor: 'black',
    shadowRadius: 2,
    shadowOpacity: 0.8,
    shadowOffset: {width: 1, height: 1},
    elevation: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  customStepBox: {
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  focusedButtonStyle: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 7},
    shadowOpacity: 0.41,
    shadowRadius: 9.11,
    elevation: 14,
  },
  headerLogo: {
    width: 120,
    height: 60,
    resizeMode: 'contain',
  },
  userInitialsContainer: {
    marginLeft: 20,
  },
  userInitialsText: {
    fontWeight: '900',
    fontSize: 32,
  },
  expertButtonContainer: {
    marginRight: 20,
  },
});

const TabNavigation = () => {
  return (
    <CopilotProvider>
      <TabNavigationWithTooltip />
    </CopilotProvider>
  );
};

export default TabNavigation;
