import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  ImagePickerResponse,
  launchImageLibrary,
} from 'react-native-image-picker';
import {PERMISSIONS, RESULTS, check, request} from 'react-native-permissions';
import EditIcon from '../assets/images/icons8-edit.svg';
import {generateGuid} from '../common/GuidGenerator';
import {UserProfile} from '../common/UserModel';
import WarningModal from '../components/WarningModal';
import {useApi} from '../context/ApiContext';
import {useTheme} from '../context/ThemeContext';
import {ImageType} from './AlertScreen';

interface IUserForm {
  firstName?: string;
  surname?: string;
  phoneNo?: string;
}

const ProfileScreen: React.FC = () => {
  const {themeStyles} = useTheme();
  const [showCameraWarningModal, setShowCameraWarningModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [userData, setUserData] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const {getUserSettings, saveProfileImage, getDocument, updateUser} = useApi();
  const [image, setImage] = useState<string | null>(null);
  const [form, setForm] = useState<IUserForm>({
    firstName: '',
    surname: '',
    phoneNo: '',
  });
  const [errors, setErrors] = useState<IUserForm>({
    firstName: '',
    surname: '',
    phoneNo: '',
  });

  const fetchProfilePicture = (fileName: string) => {
    if (!fileName) return;
    if (fileName.includes('file:///')) {
      setImage(fileName);
    } else {
      setUploading(true);
      getDocument(fileName)
        .then((result: string) => setImage(result))
        .catch(console.error)
        .finally(() => setUploading(false));
    }
  };

  const dismissKeyboard = () => Keyboard.dismiss();

  const onUpload = () => {
    if (Platform.OS === 'ios') {
      const permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
      check(permission)
        .then(result => {
          if (result === RESULTS.GRANTED || result === RESULTS.LIMITED) {
            uploadProfilePicture();
          } else if (result === RESULTS.DENIED) {
            request(permission).then(res => {
              if (res === RESULTS.GRANTED || res === RESULTS.LIMITED) {
                uploadProfilePicture();
              } else {
                setShowCameraWarningModal(true);
              }
            });
          } else {
            setShowCameraWarningModal(true);
          }
        })
        .catch(console.error);
    } else {
      uploadProfilePicture();
    }
  };

  const uploadProfilePicture = async () => {
    try {
      const result: ImagePickerResponse = await launchImageLibrary({
        mediaType: 'photo',
        selectionLimit: 1,
      });
      if (!result.didCancel && result.assets?.[0]) {
        const {fileName, type, uri} = result.assets[0];
        if (!fileName || !type || !uri) return;

        const id = generateGuid();
        const temp: ImageType = {id, name: fileName, type, uri};

        setUploading(true);
        const status = await saveProfileImage(temp);
        if (status) {
          setImage(temp.uri);
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setUploading(false);
    }
  };

  const fetchUserSettings = async () => {
    setLoading(true);
    try {
      const result: UserProfile = await getUserSettings();
      setUserData({...result, email: result.email});
      await AsyncStorage.setItem('userPhoneNumber', result.phoneNo ?? '');
      setForm({
        firstName: result.firstName,
        surname: result.surname,
        phoneNo: result.phoneNo,
      });
      if (result?.document) {
        fetchProfilePicture(result.document.fileName);
      } else {
        const user = auth().currentUser;
        setImage(user?.photoURL ?? null);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setForm(prev => ({...prev, [field]: value}));
    setErrors(prev => ({...prev, [field]: ''}));
  };

  const validateForm = () => {
    let isValid = true;
    let newErrors: IUserForm = {firstName: '', surname: '', phoneNo: ''};

    if (!form.firstName) {
      newErrors.firstName = 'First name is required.';
      isValid = false;
    }
    if (!form.surname) {
      newErrors.surname = 'Last name is required.';
      isValid = false;
    }
    if (form.phoneNo && !/^\+?[1-9]\d{9,14}$/.test(form.phoneNo)) {
      newErrors.phoneNo = 'Invalid phone number format.';
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const onSave = async () => {
    try {
      if (validateForm() && userData) {
        setLoading(true);
        const reqBody = {...userData, ...form};
        delete reqBody.id;

        const updatedUser: UserProfile = await updateUser(reqBody);
        setUserData(updatedUser);
        setForm({
          firstName: updatedUser.firstName,
          surname: updatedUser.surname,
          phoneNo: updatedUser.phoneNo,
        });
        await AsyncStorage.setItem(
          'userPhoneNumber',
          updatedUser.phoneNo ?? '',
        );
        setShowSuccessModal(true);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserSettings();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: 100,
          backgroundColor: themeStyles.background,
        }}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.centerScreen}>
            <ActivityIndicator color={themeStyles.primary} size="large" />
          </View>
        ) : userData ? (
          <TouchableWithoutFeedback onPress={dismissKeyboard}>
            <View
              style={[
                styles.container,
                {backgroundColor: themeStyles.background},
              ]}>
              <WarningModal
                visible={showCameraWarningModal}
                warning="This app does not have access to the photo library. Enable it in settings."
                onConfirm={() => setShowCameraWarningModal(false)}
              />

              {/* Success Popup */}
              <Modal
                visible={showSuccessModal}
                transparent
                animationType="fade"
                onRequestClose={() => setShowSuccessModal(false)}>
                <View style={styles.modalOverlay}>
                  <View style={styles.modalBox}>
                    <Text style={styles.modalTitle}>✅ Profile Updated</Text>
                    <Text style={styles.modalText}>
                      Your changes have been saved successfully.
                    </Text>
                    <TouchableOpacity
                      style={styles.modalButton}
                      onPress={() => setShowSuccessModal(false)}>
                      <Text style={styles.modalButtonText}>OK</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </Modal>

              {/* Profile Picture */}
              <View style={{position: 'relative'}}>
                <View style={styles.avatarWrapper}>
                  {uploading ? (
                    <ActivityIndicator
                      color={themeStyles.primary}
                      size="large"
                    />
                  ) : image ? (
                    <Image source={{uri: image}} style={styles.avatar} />
                  ) : (
                    <Text style={styles.avatarPlaceholder}>
                      {userData.firstName?.charAt(0)}
                      {userData.surname?.charAt(0)}
                    </Text>
                  )}
                </View>
                <TouchableOpacity
                  style={[
                    styles.editAvatar,
                    {backgroundColor: themeStyles.primary},
                  ]}
                  onPress={onUpload}>
                  <EditIcon fill={themeStyles.background} />
                </TouchableOpacity>
              </View>

              {/* User Info */}
              <Text style={[styles.nameText, {color: themeStyles.inputText}]}>
                {`${userData.firstName} ${userData.surname}`}
              </Text>
              <Text style={[styles.emailText, {color: themeStyles.text}]}>
                {userData.email.includes('@') ? userData.email : ''}
              </Text>

              {/* Form Inputs */}
              {renderInput(
                'First Name',
                'firstName',
                form.firstName,
                errors.firstName,
                handleInputChange,
                themeStyles,
              )}
              {renderInput(
                'Last Name',
                'surname',
                form.surname,
                errors.surname,
                handleInputChange,
                themeStyles,
              )}
              {renderInput(
                'Phone',
                'phoneNo',
                form.phoneNo,
                errors.phoneNo,
                handleInputChange,
                themeStyles,
                'phone-pad',
              )}

              <TouchableOpacity style={styles.saveButton} onPress={onSave}>
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        ) : (
          <Text>No user, please login again.</Text>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const renderInput = (
  label: string,
  field: string,
  value: string | undefined,
  error: string | undefined,
  onChange: (f: string, v: string) => void,
  themeStyles: any,
  keyboardType: 'default' | 'phone-pad' = 'default',
) => (
  <>
    <Text style={[styles.label, {color: themeStyles.text}]}>{label}</Text>
    <TextInput
      style={[
        styles.input,
        {color: themeStyles.text, backgroundColor: themeStyles.background},
      ]}
      value={value}
      placeholder={`Enter ${label}`}
      placeholderTextColor={themeStyles.text}
      onChangeText={v => onChange(field, v)}
      keyboardType={keyboardType}
    />
    {error ? (
      <Text style={[styles.error, {color: themeStyles.danger}]}>{error}</Text>
    ) : null}
  </>
);

const styles = StyleSheet.create({
  container: {flex: 1, alignItems: 'center', padding: 20},
  centerScreen: {
    height: Dimensions.get('window').height,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarWrapper: {
    width: 180,
    height: 180,
    borderRadius: 90,
    borderWidth: 3,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatar: {width: 180, height: 180, borderRadius: 90},
  avatarPlaceholder: {fontSize: 64, fontWeight: '700'},
  editAvatar: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    width: 45,
    height: 45,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nameText: {
    fontWeight: 'bold',
    fontSize: 28,
    marginTop: 16,
    textAlign: 'center',
  },
  emailText: {fontSize: 16, marginBottom: 20, textAlign: 'center'},
  label: {alignSelf: 'flex-start', marginTop: 10},
  input: {
    width: '100%',
    height: 45,
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: 15,
    marginTop: 8,
    marginBottom: 12,
  },
  error: {fontSize: 12, marginBottom: 5},
  saveButton: {
    marginTop: 20,
    width: '100%',
    backgroundColor: '#158c4e',
    padding: 14,
    borderRadius: 25,
  },
  saveButtonText: {color: '#fff', fontSize: 18, textAlign: 'center'},

  // Success Modal
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalBox: {
    backgroundColor: '#fff',
    padding: 25,
    borderRadius: 16,
    alignItems: 'center',
    width: '80%',
  },
  modalTitle: {fontSize: 20, fontWeight: 'bold', marginBottom: 10},
  modalText: {fontSize: 16, textAlign: 'center', marginBottom: 20},
  modalButton: {
    backgroundColor: '#158c4e',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  modalButtonText: {color: '#fff', fontSize: 16},
});

export default ProfileScreen;
