import appleAuth from '@invertase/react-native-apple-authentication';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Keyboard,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import AppleIcon from '../assets/images/apple-logo-svgrepo-com.svg';
import EmailIcon from '../assets/images/envelope-solid.svg';
import EyeSlashIcon from '../assets/images/eye-slash-solid.svg';
import EyeIcon from '../assets/images/eye-solid.svg';
import GoogleIcon from '../assets/images/icons8-google (1).svg';
import PasswordIcon from '../assets/images/lock-solid.svg';
import UserIcon from '../assets/images/user-solid.svg';
import {
  FieldValidation,
  ValidateField,
  ValidateForm,
} from '../common/Validation';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

type SignUpScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'SignUp'
>;

type Props = {
  navigation: SignUpScreenNavigationProp;
};

const SignUpScreen: React.FC<Props> = ({navigation}) => {
  const {themeStyles, theme} = useTheme();
  const {signup} = useAuth();
  const {
    initializing,
    signin,
    signout,
    signInWithGoogle,
    signInWithMicrosoft,
    handleAppleLogin,
  } = useAuth();

  const [form, setForm] = useState({
    firstname: '',
    surname: '',
    email: '',
    password: '',
  });
  const [isFormValid, setFormIsValid] = useState<boolean>(false);
  const [validation, setValidation] = useState({
    firstname: '',
    surname: '',
    email: '',
    password: '',
  });
  const [revealPassword, setRevealPassword] = useState<boolean>(false);
  ('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSocialLoginLoading, setIsSocialLoginLoading] = useState(false);
  const [error, setError] = useState();

  const windowWidth = Dimensions.get('window').width;
  const inputIconSize = 24;
  const socialIconSize = 30;

  const validationSchema: FieldValidation[] = [
    {
      name: 'email',
      requirements: ['Required', 'ValidEmail'],
      displayName: 'Email',
    },
    {
      name: 'password',
      requirements: ['Required'],
      displayName: 'Password',
    },
    {
      name: 'firstname',
      requirements: ['Required'],
      displayName: 'First name',
    },
    {
      name: 'surname',
      requirements: ['Required'],
      displayName: 'Surname',
    },
  ];

  const onValueChange = (name: string, value: any) => {
    let newForm = {...form, [name]: value};
    setForm(newForm);
    setFormIsValid(ValidateForm(newForm, validationSchema));
  };

  const onFieldBlur = (name: string) => {
    let schema = validationSchema.filter(x => x.name == name);

    if (schema.length > 0) {
      //@ts-ignore
      let error = ValidateField(form, schema[0]);
      setValidation({...validation, [name]: error});
    }
  };

  const handleSignUp = () => {
    setIsLoading(true);
    signup(form.email, form.password, form.firstname, form.surname)
      .catch(error => {
        setError(error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handleGoogleSingIn = async () => {
    setIsSocialLoginLoading(true);
    try {
      await GoogleSignin.hasPlayServices();
      const signInResp = await GoogleSignin.signIn();
      // Check if the user canceled or the response is incomplete
      if (!signInResp || !signInResp.data || !signInResp.data.idToken) {
        return;
      }
      // Proceed with further sign-in if valid response
      if (signInResp && signInResp.data.idToken && signInResp.data.user) {
        await signInWithGoogle(
          signInResp.data.idToken,
          signInResp.data.user.givenName!,
          signInResp.data.user.familyName!,
        );
        setError(undefined); // Successful login, clear any errors
      } else {
        throw new Error('Google id token not found!');
      }
    } catch (error: any) {
      setError(error);
    } finally {
      setIsSocialLoginLoading(false);
    }
  };
  const handleAppleSingIn = async () => {
    setIsSocialLoginLoading(true);
    try {
      if (appleAuth.isSupported) {
        await handleAppleLogin();
      } else {
        Alert.alert('Apple auth is not supported in your device ');
      }
    } catch (error: any) {
      Alert.alert(error);
      setError(error);
    } finally {
      setIsSocialLoginLoading(false);
    }
  };
  const handleMicrosoftSingIn = async () => {
    setIsSocialLoginLoading(true);
    try {
      const response = await signInWithMicrosoft();
      setError(undefined);
    } catch (error: any) {
      setError(error);
    } finally {
      setIsSocialLoginLoading(false);
    }
  };
  return (
    <TouchableWithoutFeedback onPress={() => dismissKeyboard()}>
      <View
        style={[styles.container, {backgroundColor: themeStyles.background}]}>
        <Text style={[styles.signUpHeading, {color: themeStyles.primary}]}>
          Create Account
        </Text>

        <View>
          <View style={styles.inputContainer}>
            <UserIcon
              width={inputIconSize}
              height={inputIconSize}
              fill={themeStyles.text}
              style={{marginLeft: 10}}
            />
            <TextInput
              style={[styles.input, {color: themeStyles.inputText}]}
              placeholder="First name"
              placeholderTextColor={themeStyles.text}
              onChangeText={text => onValueChange('firstname', text)}
              value={form.firstname}
              onBlur={() => onFieldBlur('firstname')}
            />
          </View>
          {validation.firstname != '' && (
            <Text style={styles.validationError}>{validation.firstname}</Text>
          )}

          <View style={styles.inputContainer}>
            <UserIcon
              width={inputIconSize}
              height={inputIconSize}
              fill={themeStyles.text}
              style={{marginLeft: 10}}
            />
            <TextInput
              style={[styles.input, {color: themeStyles.inputText}]}
              placeholder="Surname"
              placeholderTextColor={themeStyles.text}
              onChangeText={text => onValueChange('surname', text)}
              value={form.surname}
              onBlur={() => onFieldBlur('surname')}
            />
          </View>
          {validation.surname != '' && (
            <Text style={styles.validationError}>{validation.surname}</Text>
          )}

          <View style={styles.inputContainer}>
            <EmailIcon
              width={inputIconSize}
              height={inputIconSize}
              fill={themeStyles.text}
              style={{marginLeft: 10}}
            />
            <TextInput
              style={[styles.input, {color: themeStyles.inputText}]}
              placeholder="Email"
              placeholderTextColor={themeStyles.text}
              onChangeText={text => onValueChange('email', text)}
              value={form.email}
              onBlur={() => onFieldBlur('email')}
              inputMode="email"
              keyboardType="email-address"
              textContentType="emailAddress"
              autoCapitalize="none"
            />
          </View>
          {validation.email != '' && (
            <Text style={styles.validationError}>{validation.email}</Text>
          )}

          <View style={styles.inputContainer}>
            <PasswordIcon
              width={inputIconSize}
              height={inputIconSize}
              fill={themeStyles.text}
              style={{marginLeft: 10}}
            />
            <TextInput
              style={[styles.input, {color: themeStyles.inputText}]}
              placeholder="Password"
              placeholderTextColor={themeStyles.text}
              onChangeText={text => onValueChange('password', text)}
              value={form.password}
              secureTextEntry={!revealPassword}
              onBlur={() => onFieldBlur('password')}
              textContentType="password"
            />
            {revealPassword ? (
              <TouchableOpacity
                onPress={() => {
                  setRevealPassword(!revealPassword);
                }}>
                <EyeSlashIcon
                  width={inputIconSize}
                  height={inputIconSize}
                  fill={themeStyles.text}
                  style={{marginLeft: 5, marginRight: 5}}
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={() => {
                  setRevealPassword(!revealPassword);
                }}>
                <EyeIcon
                  width={inputIconSize}
                  height={inputIconSize}
                  fill={themeStyles.text}
                  style={{marginLeft: 5, marginRight: 5}}
                />
              </TouchableOpacity>
            )}
          </View>
          {validation.password != '' && (
            <Text style={styles.validationError}>{validation.password}</Text>
          )}
        </View>

        <View style={styles.lowerContainer}>
          <TouchableOpacity
            style={
              isFormValid
                ? [
                    styles.signUp,
                    {
                      marginBottom: 1,
                      backgroundColor: themeStyles.primary,
                      ...(theme === 'dark' && {
                        borderColor: themeStyles.text,
                        borderWidth: 1,
                        backgroundColor: 'transparent',
                      }),
                    },
                  ]
                : [
                    styles.signUpDisabled,
                    {
                      backgroundColor: themeStyles.primaryDisabled,
                      marginBottom: 1,
                    },
                  ]
            }
            onPress={handleSignUp}
            disabled={!isFormValid}>
            {!isLoading ? (
              <Text style={styles.signUpText}>Sign Up</Text>
            ) : (
              <ActivityIndicator color={themeStyles.background} />
            )}
          </TouchableOpacity>
          {Platform.OS == 'ios' && (
            <TouchableOpacity
              style={[
                styles.signUp,
                // eslint-disable-next-line react-native/no-inline-styles
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: themeStyles.primary,
                  ...(theme === 'dark' && {
                    borderColor: themeStyles.text,
                    borderWidth: 1,
                    backgroundColor: 'transparent',
                  }),
                },
              ]}
              onPress={handleAppleSingIn}>
              <>
                <AppleIcon
                  width={inputIconSize}
                  height={inputIconSize}
                  style={styles.icon}
                  fill="white"
                />
                <Text style={styles.signUpText}>Continue With Apple</Text>
              </>
            </TouchableOpacity>
          )}
          {Platform.OS == 'android' && (
            <TouchableOpacity
              style={[
                styles.signUp,
                // eslint-disable-next-line react-native/no-inline-styles
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 15,
                  backgroundColor: themeStyles.white,
                  borderColor: themeStyles.primary,
                  borderWidth: 1,
                  ...(theme === 'dark' && {
                    borderColor: themeStyles.text,
                    backgroundColor: 'transparent',
                  }),
                },
              ]}
              onPress={handleGoogleSingIn}>
              <>
                <GoogleIcon
                  width={inputIconSize}
                  height={inputIconSize}
                  style={styles.icon}
                />
                <Text style={[styles.signUpText, {color: themeStyles.primary}]}>
                  Continue With Google
                </Text>
                {isSocialLoginLoading && (
                  <ActivityIndicator
                    color={themeStyles.primary}
                    size={'small'}
                  />
                )}
              </>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.cancel,
              styles.btnContainer,
              {
                ...(theme === 'dark' && {
                  borderColor: themeStyles.text,
                  borderWidth: 1,
                  backgroundColor: 'transparent',
                }),
              },
            ]}
            onPress={handleCancel}>
            <Text style={styles.cancelText}>Cancel</Text>
          </TouchableOpacity>
          {error && <Text style={{color: 'red'}}>{error}</Text>}
        </View>
        {/* {!isSocialLoginLoading ? (
          <>
            {Platform.OS != 'ios' && (
              <View style={styles.logoContainer}>
                <Text style={[styles.logInWith, {color: themeStyles.text}]}>
                  or continue with
                </Text>
                <View style={styles.socialContainer}>
                  {Platform.OS == 'android' && (
                    <TouchableOpacity onPress={handleGoogleSingIn}>
                      <GoogleIcon
                        width={socialIconSize}
                        height={socialIconSize}
                        style={styles.icon}
                      />
                    </TouchableOpacity>
                  )}
                  {
                      Platform.OS == 'android' && (
                        <TouchableOpacity onPress={handleMicrosoftSingIn}>
                          <MicrosoftIcon
                            width={socialIconSize}
                            height={socialIconSize}
                            style={styles.icon}
                          />
                        </TouchableOpacity>
                      )
                    }
                </View>
              </View>
            )}
          </>
        ) : (
          <ActivityIndicator color="green" size={'large'} />
        )} */}
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  signUpHeading: {
    color: '#108a00',
    fontWeight: 'bold',
    fontSize: 25,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 25,
    padding: 8,
    width: '100%',
    height: 45,
    marginTop: 20,
  },
  input: {
    flexGrow: 1,
    color: '#000',
    marginLeft: 10,
    height: 45,
  },
  icon: {
    marginRight: 10,
  },
  signUp: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  signUpDisabled: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#ccc',
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  signUpText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cancel: {
    marginTop: 10,
    marginBottom: 15,
    width: '100%',
    borderColor: '#c5c5c5',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  cancelText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  btnContainer: {
    marginBottom: 20,
  },
  lowerContainer: {
    alignItems: 'flex-start',
    width: '100%',
  },
  logoContainer: {
    alignItems: 'center',
  },
  validationError: {
    marginTop: 5,
    marginLeft: 10,
    color: '#767676',
    fontWeight: 'bold',
  },
  logInWith: {
    marginTop: 10,
    textAlign: 'center',
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    marginLeft: 15,
  },
});

export default SignUpScreen;
