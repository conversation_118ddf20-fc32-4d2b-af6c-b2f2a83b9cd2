import React from 'react'
import { StyleSheet, Modal, Text, View, TouchableOpacity, Dimensions } from 'react-native'
import CheckIcon from '../assets/images/check-solid.svg';
import { useTheme } from '../context/ThemeContext';

type Props = {
    visible: boolean
    warning: string,
    onConfirm: () => void
}

export default function WarningModal({ visible, warning, onConfirm }: Props) {

    const { themeStyles } = useTheme();

    const windowWidth = Dimensions.get('window').width;
    const inputIconSizeYes = windowWidth * 0.05;
    const inputIconSizeNo = windowWidth * 0.04;

    return (
        <Modal visible={visible} transparent={true}>
            <View style={styles.container}>
                <View style={[styles.modal, { backgroundColor: themeStyles.background}]}>
                    <View>
                        <Text style={[styles.questionText, { color: themeStyles.text }]}>{warning}</Text>
                    </View>
                    <View style={styles.buttonsContainer}>
                        <TouchableOpacity style={[styles.button, styles.reject, { borderColor: themeStyles.text }]} onPress={onConfirm}>
                            <View style={styles.iconContainer}>
                                <CheckIcon width={inputIconSizeYes} height={inputIconSizeYes} fill={themeStyles.text} />
                                <Text style={[styles.rejectText, { color: themeStyles.text }]}>OK</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center'
    },
    modal: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 8,
        width: '90%'
    },
    questionText: {
        fontSize: 16,
    },
    buttonsContainer: {
        marginTop: 15,
        flexDirection: "row",
        justifyContent: "space-evenly"
    },
    button: {
        width: '40%',
        padding: 10,
        borderRadius: 25,
        height: 40
    },
    confirm: {
        backgroundColor: '#108a00',
    },
    confirmText: {
        color: '#fff',
        fontWeight: "bold",
        textAlign: "center",
        marginLeft: 10
    },
    reject: {
        borderWidth: 2
    },
    rejectText: {
        color: '#767676',
        fontWeight: "bold",
        textAlign: "center",
        marginLeft: 10
    },
    iconContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: -2
    },
});