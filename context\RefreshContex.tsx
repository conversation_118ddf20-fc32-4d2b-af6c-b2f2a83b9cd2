import React, { useState, useEffect, createContext, useContext, useRef } from 'react'
import auth, { FirebaseAuthTypes } from "@react-native-firebase/auth"
import { Api } from '../api/Api';

interface Props {
    children: React.ReactNode;
}

interface RefreshContextType {
    home: boolean,
    clearHome: () => void,
    documents: boolean,
    clearDocuments: () => void,
    categories: boolean,
    clearCategories: () => void,
    refreshScreens: (screens: Screen[]) => void
}

const RefreshContext = createContext<RefreshContextType>({
    home: false,
    clearHome: () => {},
    documents: true,
    clearDocuments: () => {},
    categories: true,
    clearCategories: () => {},
    refreshScreens: (screens: Screen[]) => {}
});

export const useRefresh = (): RefreshContextType => {
    const context = useContext(RefreshContext);
    return context;
}

type Screen = 'Home' | 'Documents' | 'Categories';

export const RefreshProvider: React.FC<Props> = ({ children }) => {
    const [home, setHome] = useState<boolean>(false);
    const [documents, setDocuments] = useState<boolean>(false);
    const [categories, setCategories] = useState<boolean>(false);

    return (
        <RefreshContext.Provider value={{
            home: home,
            clearHome: () => {
                setHome(false);
            },
            documents: documents,
            clearDocuments: () => {
                setDocuments(false);
            },
            categories: categories,
            clearCategories: () => {
                setCategories(false);
            },
            refreshScreens: (screens: Screen[]) => {
                setHome(home ? home : screens.includes("Home"));
                setDocuments(documents ? documents : screens.includes("Documents"));
                setCategories(categories ? categories : screens.includes("Categories"));
            }
        }}>
            { children }
        </RefreshContext.Provider>
    )
}