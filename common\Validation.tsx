export type ValidationError = {
    name: string,
    error: string
}

export const IsRequired = (value: any): boolean => {
    if (typeof (value) == "string") {
        if (!value || value.trim() == "") {
            return false;
        }
    } else {
        if (value == null || value == undefined) {
            return false;
        }
    }

    return true;
}

export const IsValidEmail = (email: string) => {
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    return emailRegex.test(email)
}

export type FieldValidation = {
    name: string,
    requirements: Requirements[],
    displayName: string,
    options?: Options,
    fields?: { fieldName: string, displayName: string }[]
}

export type Options = {
    field: string,
    value: any,
    operator: "equal" | "not equal" | "greater than" | "less than"
}

type Requirements = 'Required' | "RequiredIf" | 'ValidEmail' | "DateInFuture" | 'RequiredOr';

export const ValidateForm = (form: any, schema: FieldValidation[]): boolean => {
    let isValid: boolean = true;
    schema.forEach(schemaField => {
        let formField = form[schemaField.name];
        isValid = isValid ? ValidateField(form, schemaField) == "" : false;
    })
    return isValid;
}

export const ValidateField = (form: any, schema: FieldValidation) => {
    let isValid: string = "";

    schema.requirements.forEach(requirement => {
        if (isValid != "") return;

        if (requirement == "Required") {
            if (!IsRequired(form[schema.name])) {
                isValid = `${schema.displayName} is required`;
            }
        } else if (requirement == "RequiredIf") {
            var isRequired = false;

            switch (schema.options?.operator) {
                case "equal":
                    isRequired = form[schema.options.field] == schema.options.value;
                    break;
                case "not equal":
                    isRequired = form[schema.options.field] != schema.options.value;
                    break;
                case "greater than":
                    isRequired = form[schema.options.field] <= schema.options.value;
                    break;
                case "less than":
                    isRequired = form[schema.options.field] >= schema.options.value;
                    break;
            }

            if (isRequired && !IsRequired(form[schema.name])) {
                isValid = `${schema.displayName} is required`;
            }
        } else if (requirement == "ValidEmail") {
            if (!IsValidEmail(form[schema.name])) {
                isValid = `A valid email is required`;
            }
        } else if (requirement == "DateInFuture") {
            var now = new Date();
            var input = new Date(form[schema.name]);

            if (input < now) {
                isValid = `${schema.displayName} must be a date in the future`
            }
        } else if (requirement == 'RequiredOr' && schema.fields) {
            let status = false;
            schema.fields.forEach((field: {fieldName: string, displayName: string}) => {

                if (IsRequired(form[field.fieldName])) {
                    status = true;
                    return;
                }

            })
            if (!status) {
                schema.fields.forEach((field: {fieldName: string, displayName: string}, index: number) => {
                    if (index == 0) {
                        isValid += field.displayName;
                    } else {
                        isValid += ` or ${field.displayName}`
                    }
                })
                isValid += " is required."
            }
        }
    })

    return isValid;
}