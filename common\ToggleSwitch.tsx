import React from 'react';
import { View, Switch, StyleSheet, Platform } from 'react-native';
import { useTheme } from '../context/ThemeContext';

interface ToggleSwitchProps {
    isEnabled: boolean;
    toggleSwitch: () => void;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({ isEnabled, toggleSwitch }) => {
    const { themeStyles } = useTheme();

    return (
        <View style={styles.container}>
            <Switch
                trackColor={{ false: themeStyles.text, true: themeStyles.primary }}
                thumbColor={isEnabled && Platform.OS === "android" ? themeStyles.primary : themeStyles.primaryDisabled}
                ios_backgroundColor="#3e3e3e"
                onValueChange={toggleSwitch}
                value={isEnabled}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: 60,
        height: 30,
    }
});


export default ToggleSwitch;
