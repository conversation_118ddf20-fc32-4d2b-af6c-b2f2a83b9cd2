# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)



platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

  lane :beta do
    gradle(task: "clean")
    gradle(task: 'assemble', build_type: 'Release')
    upload_to_play_store(track: 'beta')
    slack(message: 'Successfully distributed a new beta build')
  end

  lane :playstoreInternal do
    gradle(
      task: 'bundle',
      build_type: 'Release'
      )
      upload_to_play_store_internal_app_sharing
  end

  lane :deploy_internal_testing do
    gradle(
      task: "bundle",
      build_type: "Release"
    )
  
    upload_to_play_store(
      track: "internal",
      version_name:"",
      version_code:0,
      release_status: 'draft',
      package_name: "flerts.prod.com",
      aab: "/Users/<USER>/Desktop/Flerts-Update/Flerts/android/app/build/outputs/bundle/release/app-release.aab",
      json_key: "/Users/<USER>/Desktop/Flerts-Repo/Flerts/android/api-key.json",
      skip_upload_apk: true
    )
  end
  

  desc "Build release app bundle"
  lane :build_bundle do
    gradle(task: "clean bundleRelease")
  end

end