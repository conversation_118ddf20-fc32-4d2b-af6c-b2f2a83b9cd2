import React, { useEffect } from 'react'
import { StyleSheet, Modal, Text, View, TouchableOpacity, Dimensions, TextInput, ActivityIndicator } from 'react-native'
import CheckIcon from '../assets/images/check-solid.svg';
import CrossIcon from '../assets/images/x-solid.svg';
import BackArrow from '../assets/images/arrow-left-solid.svg';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

type Props = {
    visible: boolean
    onComplete: () => void,
    onCancel: () => void,
    startScreen?: Screen,
}

type Screen = "recommend_mfa" | "password" | "phone_number" | "verification_code" | "success";

export default function EnrollMFAModal({ visible, onComplete, onCancel, startScreen }: Props) {

    const { themeStyles } = useTheme();
    const { enrollMFA, verifyNewMFA, confirmPassword, isProviderLogin } = useAuth();

    const windowWidth = Dimensions.get('window').width;
    const inputIconSizeYes = 16;
    const inputIconSizeNo = 15;

    const [screen, setScreen] = React.useState<Screen>(startScreen ?? "password");
    const [password, setPassword] = React.useState<string>("");
    const [phoneNumber, setPhoneNumber] = React.useState<string>("");
    const [verificationId, setVerificationId] = React.useState<string>("");
    const [verificationCode, setVerificationCode] = React.useState<string>("");
    const [isLoading, setIsLoading] = React.useState<boolean>(false);
    const [error, setError] = React.useState<string>("");

    const onPhoneNumberChange = (value: string) => {
        setPhoneNumber(value);
    }

    const onVerificationCodeChanged = (value: string) => {
        setVerificationCode(value);
    }

    const onEnableNow = () => {
        setScreen("phone_number");
    }

    const onConfirmPassword = () => {
        setIsLoading(true);

        confirmPassword(password)
            .then(() => {
                setError("");
                setScreen('phone_number');
            })
            .catch((error) => {
                setError(error);
                console.log(error);
            })
            .finally(() => {
                setIsLoading(false);
            });
    }

    const onSendCode = () => {
        setError("");
        setIsLoading(true);

        enrollMFA(phoneNumber)
            .then(verificationId => {
                setError("");
                setVerificationId(verificationId);
                setScreen("verification_code");
            })
            .catch(error => {
                // show validation error
                setError(error);
                console.log(error);
            })
            .finally(() => {
                setIsLoading(false);
            });
    }

    const onVerififyCode = () => {
        setIsLoading(true);
        verifyNewMFA(verificationId, verificationCode)
            .then(() => {
                setError("");
                setScreen("success");
            })
            .catch((error) => {
                setError(error);
                console.log(error);
            })
            .finally(() => {
                setIsLoading(false);
            })
    }

    const onPasswordBack = () => {
        setPassword("");
        onCancel();
    }

    const onPhoneNumberBack = () => {
        setPhoneNumber("");
       if(!isProviderLogin) setScreen("password");
        onPasswordBack();
    }

    const onVerificationCodeBack = () => {
        setVerificationCode("");
        setScreen("phone_number");
    }

    return (
        <Modal visible={visible} transparent={true}>
            {screen == "recommend_mfa" &&
                <View style={styles.container}>
                    <View style={[styles.modal, { backgroundColor: themeStyles.background }]}>
                        <View>
                            <Text style={[styles.questionText, { color: themeStyles.text }]}>To keep your account and data secure we recommend enabling two factor authentication on your account</Text>
                        </View>
                        <View style={[{ flexDirection: "column" }, styles.buttonsContainer]}>
                            <TouchableOpacity style={[styles.button, { backgroundColor: themeStyles.primary }, { width: "100%" }]} onPress={onEnableNow}>
                                <View style={styles.iconContainer}>
                                    <Text style={[styles.confirmText, { color: themeStyles.background }]}>Enable</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={onPasswordBack}>
                                <Text style={[styles.rejectText, { color: themeStyles.text }]}>Skip for now</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            }
            {screen == "password" &&
                <View style={styles.container}>
                    <View style={[styles.modal, { backgroundColor: themeStyles.background }]}>
                        <View>
                            <Text style={[styles.questionText, { color: themeStyles.text }]}>Please confirm your password to enable two factor authentication</Text>
                        </View>
                        <View style={[{ flexDirection: "column" }, styles.buttonsContainer]}>
                            <TextInput
                                placeholder='Password'
                                placeholderTextColor={themeStyles.text}
                                style={[styles.input, { color: themeStyles.text, backgroundColor: themeStyles.background }]}
                                onChangeText={value => setPassword(value)}
                                value={password}
                                textContentType="password"
                                secureTextEntry
                            />
                            {error != "" && (
                                <Text style={styles.error}>{error}</Text>
                            )}
                            <TouchableOpacity style={[styles.button, { backgroundColor: themeStyles.primary }, { width: "100%" }]} onPress={onConfirmPassword}>
                                <View style={styles.iconContainer}>
                                    {isLoading ? <ActivityIndicator color={themeStyles.background} /> : <Text style={[styles.confirmText, { color: themeStyles.background }]}>Confirm</Text>}
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity style={[styles.button, styles.reject, { borderColor: themeStyles.text }, { width: "100%" }]} onPress={onPasswordBack}>
                                <View style={styles.iconContainer}>
                                    <BackArrow width={inputIconSizeNo} height={inputIconSizeNo} fill={themeStyles.text} />
                                    <Text style={[styles.rejectText, { color: themeStyles.text }]}>Back</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            }
            {screen == "phone_number" &&
                <View style={styles.container}>
                    <View style={[styles.modal, { backgroundColor: themeStyles.background }]}>
                        <View>
                            <Text style={[styles.questionText, { color: themeStyles.text }]}>Enter the phone number to receive two factor authentication codes to</Text>
                        </View>
                        <View style={[{ flexDirection: "column" }, styles.buttonsContainer]}>
                            <TextInput
                                placeholder='+440123456789'
                                placeholderTextColor={themeStyles.text}
                                style={[styles.input, { color: themeStyles.text, backgroundColor: themeStyles.background }]}
                                onChangeText={onPhoneNumberChange}
                                value={phoneNumber}
                                keyboardType='phone-pad'
                            />
                            {error != "" && (
                                <Text style={styles.error}>{error}</Text>
                            )}
                            <TouchableOpacity style={[styles.button, { backgroundColor: themeStyles.primary }, { width: "100%" }]} onPress={onSendCode}>
                                <View style={styles.iconContainer}>
                                    {isLoading ? <ActivityIndicator color={themeStyles.background} /> : <Text style={[styles.confirmText, { color: themeStyles.background }]}>Send code</Text>}
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity style={[styles.button, styles.reject, { borderColor: themeStyles.text }, { width: "100%" }]} onPress={onPhoneNumberBack}>
                                <View style={styles.iconContainer}>
                                    <BackArrow width={inputIconSizeNo} height={inputIconSizeNo} fill={themeStyles.text} />
                                    <Text style={[styles.rejectText, { color: themeStyles.text }]}>Back</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            }
            {screen == "verification_code" &&
                <View style={styles.container}>
                    <View style={[styles.modal, { backgroundColor: themeStyles.background }]}>
                        <View>
                            <Text style={[styles.questionText, { color: themeStyles.text }]}>Enter the code sent to phone number ending {phoneNumber.slice(-4)}</Text>
                        </View>
                        <View style={[{ flexDirection: "column" }, styles.buttonsContainer]}>
                            <TextInput
                                placeholder='Verification code'
                                placeholderTextColor={themeStyles.text}
                                style={[styles.input, { color: themeStyles.text, backgroundColor: themeStyles.background }]}
                                onChangeText={onVerificationCodeChanged}
                                value={verificationCode}
                                keyboardType='numeric'
                            />
                            {error != "" && (
                                <Text style={styles.error}>{error}</Text>
                            )}
                            <TouchableOpacity style={[styles.button, { backgroundColor: themeStyles.primary }, { width: "100%" }]} onPress={onVerififyCode}>
                                <View style={styles.iconContainer}>
                                    {isLoading ? <ActivityIndicator color={themeStyles.background} /> :
                                        <>
                                            <CheckIcon width={inputIconSizeYes} height={inputIconSizeYes} fill={themeStyles.background} />
                                            <Text style={[styles.confirmText, { color: themeStyles.background }]}>Verify</Text>
                                        </>
                                    }
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity style={[styles.button, styles.reject, { borderColor: themeStyles.text }, { width: "100%" }]} onPress={onVerificationCodeBack}>
                                <View style={styles.iconContainer}>
                                    <BackArrow width={inputIconSizeNo} height={inputIconSizeNo} fill={themeStyles.text} />
                                    <Text style={[styles.rejectText, { color: themeStyles.text }]}>Back</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            }
            {screen == "success" &&
                <View style={styles.container}>
                    <View style={[styles.modal, { backgroundColor: themeStyles.background }]}>
                        <CheckIcon with={100} height={100} fill={themeStyles.primary}/>
                        <View style={{marginTop: 30}}>
                            <Text style={[styles.questionText, { color: themeStyles.text }]}>Two factor authentication has been enabled on your account</Text>
                        </View>
                        <View style={[{ flexDirection: "column" }, styles.buttonsContainer]}>
                            <TouchableOpacity style={[styles.button, { backgroundColor: themeStyles.primary }, { width: "100%" }]} onPress={onComplete}>
                                <View style={styles.iconContainer}>
                                    <Text style={[styles.confirmText, { color: themeStyles.background }]}>OK</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            }
        </Modal>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center'
    },
    modal: {
        padding: 20,
        borderRadius: 8,
        width: '90%',
    },
    questionText: {
        fontSize: 16,
    },
    buttonsContainer: {
        marginTop: 15,
        justifyContent: "space-evenly",
        alignItems: "center"
    },
    button: {
        padding: 10,
        borderRadius: 25,
        height: 40,
        marginBottom: 10
    },
    confirmText: {
        color: '#fff',
        fontWeight: "bold",
        textAlign: "center",
        marginLeft: 10
    },
    reject: {
        borderWidth: 2
    },
    rejectText: {
        color: '#767676',
        fontWeight: "bold",
        textAlign: "center",
        marginLeft: 10
    },
    iconContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: -2
    },
    input: {
        height: 40,
        borderColor: '#ccc',
        borderWidth: 1,
        paddingHorizontal: 10,
        color: '#000',
        backgroundColor: '#fff',
        borderRadius: 25,
        marginVertical: 10,
        zIndex: -1,
        width: '100%'
    },
    error: {
        textAlign: 'center',
        marginBottom: 5,
        marginLeft: 10,
        color: "#767676",
        fontWeight: "bold"
    }
});