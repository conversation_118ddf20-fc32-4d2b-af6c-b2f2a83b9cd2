PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.71.10)
  - FBReactNativeSpec (0.71.10):
    - RCT-<PERSON>olly (= 2021.07.22.00)
    - RCTRequired (= 0.71.10)
    - RCTTypeSafety (= 0.71.10)
    - React-Core (= 0.71.10)
    - React-jsi (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - Firebase (10.20.0):
    - Firebase/Core (= 10.20.0)
  - Firebase/Auth (10.20.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.20.0)
  - Firebase/Core (10.20.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.20.0)
  - Firebase/CoreOnly (10.20.0):
    - FirebaseCore (= 10.20.0)
  - Firebase/Messaging (10.20.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.20.0)
  - FirebaseAnalytics (10.20.0):
    - FirebaseAnalytics/AdIdSupport (= 10.20.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.20.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.20.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.20.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.20.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.20.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.20.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - hermes-engine (0.71.10):
    - hermes-engine/Pre-built (= 0.71.10)
  - hermes-engine/Pre-built (0.71.10)
  - libevent (2.1.12)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.71.10)
  - RCTTypeSafety (0.71.10):
    - FBLazyVector (= 0.71.10)
    - RCTRequired (= 0.71.10)
    - React-Core (= 0.71.10)
  - React (0.71.10):
    - React-Core (= 0.71.10)
    - React-Core/DevSupport (= 0.71.10)
    - React-Core/RCTWebSocket (= 0.71.10)
    - React-RCTActionSheet (= 0.71.10)
    - React-RCTAnimation (= 0.71.10)
    - React-RCTBlob (= 0.71.10)
    - React-RCTImage (= 0.71.10)
    - React-RCTLinking (= 0.71.10)
    - React-RCTNetwork (= 0.71.10)
    - React-RCTSettings (= 0.71.10)
    - React-RCTText (= 0.71.10)
    - React-RCTVibration (= 0.71.10)
  - React-callinvoker (0.71.10)
  - React-Codegen (0.71.10):
    - FBReactNativeSpec
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.10)
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/Default (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/DevSupport (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.10)
    - React-Core/RCTWebSocket (= 0.71.10)
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-jsinspector (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-Core/RCTWebSocket (0.71.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.10)
    - React-cxxreact (= 0.71.10)
    - React-hermes
    - React-jsi (= 0.71.10)
    - React-jsiexecutor (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - Yoga
  - React-CoreModules (0.71.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.10)
    - React-Codegen (= 0.71.10)
    - React-Core/CoreModulesHeaders (= 0.71.10)
    - React-jsi (= 0.71.10)
    - React-RCTBlob
    - React-RCTImage (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-cxxreact (0.71.10):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.10)
    - React-jsi (= 0.71.10)
    - React-jsinspector (= 0.71.10)
    - React-logger (= 0.71.10)
    - React-perflogger (= 0.71.10)
    - React-runtimeexecutor (= 0.71.10)
  - React-hermes (0.71.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.71.10)
    - React-jsi
    - React-jsiexecutor (= 0.71.10)
    - React-jsinspector (= 0.71.10)
    - React-perflogger (= 0.71.10)
  - React-jsi (0.71.10):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.10)
    - React-jsi (= 0.71.10)
    - React-perflogger (= 0.71.10)
  - React-jsinspector (0.71.10)
  - React-logger (0.71.10):
    - glog
  - react-native-config (1.5.1):
    - react-native-config/App (= 1.5.1)
  - react-native-config/App (1.5.1):
    - React-Core
  - react-native-date-picker (5.0.9):
    - React-Core
  - react-native-image-picker (5.7.0):
    - React-Core
  - react-native-safe-area-context (4.10.1):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-webview (13.8.6):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-perflogger (0.71.10)
  - React-RCTActionSheet (0.71.10):
    - React-Core/RCTActionSheetHeaders (= 0.71.10)
  - React-RCTAnimation (0.71.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.10)
    - React-Codegen (= 0.71.10)
    - React-Core/RCTAnimationHeaders (= 0.71.10)
    - React-jsi (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-RCTAppDelegate (0.71.10):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.10):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.10)
    - React-Core/RCTBlobHeaders (= 0.71.10)
    - React-Core/RCTWebSocket (= 0.71.10)
    - React-jsi (= 0.71.10)
    - React-RCTNetwork (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-RCTImage (0.71.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.10)
    - React-Codegen (= 0.71.10)
    - React-Core/RCTImageHeaders (= 0.71.10)
    - React-jsi (= 0.71.10)
    - React-RCTNetwork (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-RCTLinking (0.71.10):
    - React-Codegen (= 0.71.10)
    - React-Core/RCTLinkingHeaders (= 0.71.10)
    - React-jsi (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-RCTNetwork (0.71.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.10)
    - React-Codegen (= 0.71.10)
    - React-Core/RCTNetworkHeaders (= 0.71.10)
    - React-jsi (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-RCTSettings (0.71.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.10)
    - React-Codegen (= 0.71.10)
    - React-Core/RCTSettingsHeaders (= 0.71.10)
    - React-jsi (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-RCTText (0.71.10):
    - React-Core/RCTTextHeaders (= 0.71.10)
  - React-RCTVibration (0.71.10):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.10)
    - React-Core/RCTVibrationHeaders (= 0.71.10)
    - React-jsi (= 0.71.10)
    - ReactCommon/turbomodule/core (= 0.71.10)
  - React-runtimeexecutor (0.71.10):
    - React-jsi (= 0.71.10)
  - ReactCommon/turbomodule/bridging (0.71.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.10)
    - React-Core (= 0.71.10)
    - React-cxxreact (= 0.71.10)
    - React-jsi (= 0.71.10)
    - React-logger (= 0.71.10)
    - React-perflogger (= 0.71.10)
  - ReactCommon/turbomodule/core (0.71.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.10)
    - React-Core (= 0.71.10)
    - React-cxxreact (= 0.71.10)
    - React-jsi (= 0.71.10)
    - React-logger (= 0.71.10)
    - React-perflogger (= 0.71.10)
  - RecaptchaInterop (100.0.0)
  - RNAppleAuthentication (2.4.0):
    - React-Core
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNCPicker (2.7.5):
    - React-Core
  - RNDateTimePicker (5.1.0):
    - React-Core
  - RNFBApp (18.9.0):
    - Firebase/CoreOnly (= 10.20.0)
    - React-Core
  - RNFBAuth (18.9.0):
    - Firebase/Auth (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (18.9.0):
    - Firebase/Messaging (= 10.20.0)
    - FirebaseCoreExtension (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNGoogleSignin (13.0.1):
    - GoogleSignIn (~> 7.1)
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (3.8.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.31.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTImage
  - RNShare (11.0.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNSVG (13.14.0):
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - Firebase/Auth
  - Firebase/Core
  - FirebaseCore
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - libevent
    - nanopb
    - PromisesObjC
    - RecaptchaInterop

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  boost: 0a937fbcfdd646fca221c4f1d9750d7ccfdfc2dc
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: ddb55c55295ea51ed98aa7e2e08add2f826309d5
  FBReactNativeSpec: 90fc1a90b4b7a171e0a7c20ea426c1bf6ce4399c
  Firebase: 10c8cb12fb7ad2ae0c09ffc86cd9c1ab392a0031
  FirebaseAnalytics: a2731bf3670747ce8f65368b118d18aa8e368246
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: 9c5c400d2c3055d8ae3a0284944c86fa95d48dac
  FirebaseCore: 28045c1560a2600d284b9c45a904fe322dc890b6
  FirebaseCoreExtension: 0659f035b88c5a7a15a9763c48c2e6ca8c0a2977
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 06c414a21b122396a26847c523d5c370f8325df5
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: bb3c564c3efb933136af0e94899e0a46167466a8
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: d27603b55a48402501ad1928c05411dae9cd6b85
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 8dc08ca5a393b48b1c523ab6220dfdcc0fe000ad
  RCTRequired: 8ef706f91e2b643cd32c26a57700b5f24fab0585
  RCTTypeSafety: 5fbddd8eb9242b91ac0d901c01da3673f358b1b7
  React: e5d2d559e89d256a1d6da64d51adaecda9c8ddae
  React-callinvoker: 352ecbafbdccca5fdf4aed99c98ae5b7fc28e39b
  React-Codegen: 4b8e255e5330e8b75c9edb3bd55e9b252d482aca
  React-Core: 969a1650b84f74675f2950c4f3371588c7509585
  React-CoreModules: 14c57993815b7dd435080306c17b36e5a74bf874
  React-cxxreact: adf4df2983c4181f178409b2f4375393bed20aa0
  React-hermes: 60a7d0723786a8ab946898668b84861d99aff894
  React-jsi: 5ed95faf265f8b99378e8e5b1710fd6c397f5787
  React-jsiexecutor: 4119c9beed33e745363bbd77ac3ea58e75573ba7
  React-jsinspector: cdc854f8b13abd202afa54bc12578e5afb9cfae1
  React-logger: 87a7af8c8c8c50ee0609f93ff702aa842a4ff3db
  react-native-config: 136f9755ccc991cc6438053a44363259ad4c7813
  react-native-date-picker: 8504514d3f83c35252cc13b256712ae37ce25169
  react-native-image-picker: b981b88b807605540b2de3245c76e9729f418b37
  react-native-safe-area-context: 8c70551c8688cd584a53487aa1b9361e991a3b4a
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-webview: 85f7711a5d3bd8de82fa422c5c781665ba85f73c
  React-perflogger: 217095464d5c4bb70df0742fa86bf2a363693468
  React-RCTActionSheet: 8deae9b85a4cbc6a2243618ea62a374880a2c614
  React-RCTAnimation: 59c62353a8b59ce206044786c5d30e4754bffa64
  React-RCTAppDelegate: af11609a2cbbe8e27e0d7b8c21887360cc766a7c
  React-RCTBlob: e38fbde59f712fe4f96c11a8688e8b7e007a9005
  React-RCTImage: 36c0324ff499802b9874d6803ca72026e90434f6
  React-RCTLinking: 401aec3a01b18c2c8ed93bf3a6758b87e617c58d
  React-RCTNetwork: cb25b9f2737c3aa2cde0fe0bd7ff7fabf7bf9ad0
  React-RCTSettings: cb6ae9f656e1c880500c2ecbe8e72861c2262afa
  React-RCTText: 7404fd01809244d79d456f92cfe6f9fbadf69209
  React-RCTVibration: d13cc2d63286c633393d3a7f6f607cc2a09ec011
  React-runtimeexecutor: a9a1cd79996c9a0846e3232ecb25c64e1cc0172e
  ReactCommon: ad2d8938bab0753116231084c07d7a4bc7eb8537
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  RNAppleAuthentication: 8d313d93fe2238d6b7ff0a39c67ebcf298d96653
  RNCAsyncStorage: aa75595c1aefa18f868452091fa0c411a516ce11
  RNCMaskedView: 4c5ee1c8667d56077246cc6d1977f77393923560
  RNCPicker: 15ad063c72ecba748be9edea62a5bdce53090aa6
  RNDateTimePicker: 8e60be27e06a4ff43da6bcc45be72e036bd33e4b
  RNFBApp: aadada421dfe12b21692f6973e9501f45b0057ce
  RNFBAuth: 60b64ab5cfcf7c94069e53d6085078343366c671
  RNFBMessaging: 66535c56b29b5c111deb3e61999069345f1ebe74
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: 6572a5f44759900730562b418da289c373de8d06
  RNGoogleSignin: b3fb27020830c3e92f92c71628533922d9b6876d
  RNPermissions: 2eb2fe292dc937ef089fd24a8a094dd58c059e70
  RNReanimated: 984253cb42cd465842541413968be57d9407d91d
  RNScreens: 448026fcd1beb88770b0a67a871a3d1bf9cdde0a
  RNShare: ff2e58f8431f7f5e8b9f9fae49cbeb0d2bbca27c
  RNSVG: 4cab00c621b328a4a2fedaaedc15d8822216723e
  RNVectorIcons: 5784330be9dddb5474e8b378d5f6947996c84e55
  Yoga: e7ea9e590e27460d28911403b894722354d73479

PODFILE CHECKSUM: 10337d0dba39b48361d6c194ff161a1970bd6225

COCOAPODS: 1.16.2
