// For generate relase keystore

keytool -genkeypair -v -keystore release.keystore -alias androidreleasekey -keyalg RSA -keysize 2048 -validity 10000 -storepass fl4rts -keypass fl4rts -dname "CN=flerts, OU=flerts, O=flerts, L=flerts, S=flerts, C=FL"

//For generate certificate for relase keystore (under android/app)
eytool -exportcert -alias androidreleasekey -keystore .\release.keystore -file certificate.crt -storepass fl4rts -rfc