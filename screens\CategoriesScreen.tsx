import {StackNavigationProp} from '@react-navigation/stack';
import React, {JSXElementConstructor, useState, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
  Dimensions,
  RefreshControl,
} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {useApi} from '../context/ApiContext';
import {RootStackParamList} from '../types';
import IconDataList from '../common/IconDataList';
import PlusIcon from '../assets/images/circle-plus-solid.svg';
import {CategoryType} from '../api/types/CategoryType';
import ErrorMessage from '../components/ErrorMessage';
import Center from '../components/Center';
import SwipeableListItem from '../components/SwipeableListItem';
import {useTheme} from '../context/ThemeContext';
import ConfirmModal from '../components/ConfirmModal';
import {useRefresh} from '../context/RefreshContex';
import {ScrollView} from 'react-native-gesture-handler';

type ItemProps = {
  item: CategoryType;
  onPress: () => void;
  backgroundColor: string;
  textColor: string;
};

type DeleteCategory = {
  categoryId: string;
  isCustomCategory: boolean;
};

type SubCategoriesScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'SubCategories'
>;

type Props = {
  navigation: SubCategoriesScreenNavigationProp;
  route: any;
};

const CategoriesScreen: React.FC<Props> = ({navigation, route}) => {
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedId, setSelectedId] = useState<string>('');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [categoryToDelete, setCategoryToDelete] =
    useState<DeleteCategory | null>(null);
  const [listKey, setListKey] = useState<number>(0);

  const {themeStyles} = useTheme();
  const {
    categories: refreshCategories,
    clearCategories,
    refreshScreens,
  } = useRefresh();
  const {getCategories, getSubCategories, deleteCategory} = useApi();

  const windowWidth = Dimensions.get('window').width;
  const inputIconSize = 32;
  const plusIconSize = Math.min(windowWidth * 0.09, 65);
  const plusIconButtonSize = Math.min(windowWidth * 0.13, 100);
  const plusIconButtonBorderRadius = Math.min(windowWidth * 0.03, 30);

  const iconDataList = new IconDataList();

  useEffect(() => {
    setIsLoading(true);
    fetchData();
  }, []);

  useEffect(() => {
    if (refreshCategories) {
      setIsLoading(true);
      fetchData();

      clearCategories();
    }
  }, [refreshCategories]);

  // Reset swipe state when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      // Force re-render of FlatList to reset all swipeable items
      setListKey(prev => prev + 1);
    }, [])
  );

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  const fetchData = () => {
    getCategories(false)
      .then(result => {
        setCategories(result);
        setIsError(false);
      })
      .catch(error => {
        console.log(error);
        setIsError(true);
      })
      .finally(() => {
        setIsLoading(false);
        setRefreshing(false);
      });
  };

  const onDelete = () => {
    setCategoryToDelete(null);
    if (categoryToDelete) {
      deleteCategory(
        categoryToDelete.categoryId,
        categoryToDelete.isCustomCategory,
      )
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          setRefreshing(true);
          refreshScreens(['Home', 'Categories', 'Documents']);
        });
    }
  };

  const renderItem = ({item}: {item: CategoryType}) => {
    const displaySubCategory = (item: CategoryType) => {
      if (item.isCustomCategory) {
        navigation.navigate('Alert', {categoryId: item.categoryId});
      }

      getSubCategories(item.categoryId, false)
        .then(result => {
          if (result && result.length > 0) {
            navigation.navigate('SubCategories', {
              categoryId: item.categoryId,
              _subCategories: result,
            });
          } else {
            navigation.navigate('Alert', {categoryId: item.categoryId});
          }
        })
        .catch(error => {
          navigation.navigate('SubCategories', {categoryId: item.categoryId});
        });

      setSelectedId(item.categoryId);
    };

    item.icon = iconDataList.getIconByName(item.iconName);

    const Item = ({item, onPress, backgroundColor, textColor}: ItemProps) => (
      <TouchableOpacity
        key={item.categoryId}
        onPress={onPress}
        style={[styles.item, {backgroundColor}]}>
        <View style={{flexDirection: 'row'}}>
          <View
            style={{
              borderColor: '#ccc',
              borderRightWidth: 2,
              paddingRight: 10,
              justifyContent: 'center',
            }}>
            {item.icon != null ? (
              <item.icon
                width={inputIconSize}
                height={inputIconSize}
                fill={themeStyles.primary}
              />
            ) : null}
          </View>
          <View style={{marginLeft: 10}}>
            <Text style={[styles.title, {color: themeStyles.primary}]}>
              {item.name}
            </Text>
            <Text style={[styles.description, {color: themeStyles.text}]}>
              {item.description}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );

    return (
      <SwipeableListItem
        onDelete={() =>
          setCategoryToDelete({
            categoryId: item.categoryId,
            isCustomCategory: item.isCustomCategory,
          })
        }>
        <Item
          item={item}
          onPress={() => displaySubCategory(item)}
          backgroundColor={themeStyles.background}
          textColor={themeStyles.primary}
        />
      </SwipeableListItem>
    );
  };

  const addCategory = () => {
    navigation.navigate('Category');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ConfirmModal
        visible={categoryToDelete != null}
        onConfirm={onDelete}
        onReject={() => setCategoryToDelete(null)}
        question="Are you sure you want to delete this category? This will delete all alerts of this type, this action cannot be"
      />
      <View style={[styles.content, {backgroundColor: themeStyles.background}]}>
        {!isLoading ? (
          !isError ? (
            categories?.length > 0 ? (
              <>
                <FlatList
                  key={listKey}
                  data={categories}
                  renderItem={renderItem}
                  keyExtractor={item => item.categoryId}
                  showsVerticalScrollIndicator={false}
                  showsHorizontalScrollIndicator={false}
                  refreshControl={
                    <RefreshControl
                      onRefresh={onRefresh}
                      refreshing={refreshing}
                    />
                  }
                />
              </>
            ) : (
              <ScrollView
                contentContainerStyle={{flexGrow: 1, justifyContent: 'center'}}
                showsHorizontalScrollIndicator={false}
                                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                  />
                }
                showsVerticalScrollIndicator={false}>
                <Center>
                  <Text
                    style={{
                      textAlignVertical: 'center',
                      textAlign: 'center',
                      color: themeStyles.text,
                    }}>
                    You have no categories to show. Tap the plus (+) to create
                    one.
                  </Text>
                </Center>
              </ScrollView>
            )
          ) : (
            <ErrorMessage onRefresh={onRefresh} refreshing={refreshing} />
          )
        ) : (
          <Center>
            <ActivityIndicator size="large" color={themeStyles.primary} />
          </Center>
        )}
      </View>
      <View style={{flexDirection: 'row-reverse'}}>
        <TouchableOpacity
          style={[
            styles.plusIconButton,
            {
              backgroundColor: themeStyles.primary,
              marginRight: 20,
              width: plusIconButtonSize,
              height: plusIconButtonSize,
              borderRadius: plusIconButtonBorderRadius,
            },
          ]}
          onPress={() => addCategory()}>
          <PlusIcon
            width={plusIconSize}
            height={plusIconSize}
            fill={themeStyles.background}
            style={styles.raised}
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  heading: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  item: {
    padding: 20,
    marginVertical: 5,
    marginHorizontal: 2,
    borderColor: '#767676',
    borderWidth: 1,
    borderRadius: 10,
  },
  title: {
    fontSize: 18,
    color: '#108a00',
    paddingRight: 30,
  },
  description: {
    fontSize: 16,
    paddingRight: 30,
  },
  plusIconButton: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    alignSelf: 'center',
    marginRight: 20,
    bottom: 20,
  },
  raised: {
    shadowColor: 'black',
    shadowRadius: 2,
    shadowOpacity: 0.8,
    shadowOffset: {width: 1, height: 1},
  },
});

export default CategoriesScreen;
