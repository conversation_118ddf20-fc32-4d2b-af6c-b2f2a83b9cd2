{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android --variant=developmentdebug --appId=flerts.prod.com", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "start:reset": "react-native start --reset-cache", "log:android": "react-native log-android", "log:ios": "react-native log-ios", "debug": "react-native start --reset-cache --verbose", "test": "jest", "android:build-release": "cd android && gradlew clean && gradlew assembleRelease", "android:bundle-release": "cd android && gradlew clean && gradlew bundleRelease"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.0", "@react-native-async-storage/async-storage": "^1.18.2", "@react-native-community/datetimepicker": "^5.1.0", "@react-native-community/masked-view": "^0.1.11", "@react-native-firebase/app": "^18.2.0", "@react-native-firebase/auth": "^18.1.0", "@react-native-firebase/messaging": "^18.6.1", "@react-native-google-signin/google-signin": "^13.0.1", "@react-native-picker/picker": "^2.4.10", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "@react-navigation/stack": "^6.3.16", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-native": "0.71.10", "react-native-app-intro-slider": "^4.0.4", "react-native-config": "^1.5.1", "react-native-copilot": "^3.3.2", "react-native-date-picker": "^5.0.9", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~1.10.3", "react-native-image-picker": "^5.6.0", "react-native-modal": "^13.0.1", "react-native-permissions": "^3.9.2", "react-native-reanimated": "^3.3.0", "react-native-reanimated-carousel": "^3.5.1", "react-native-root-siblings": "^4.1.1", "react-native-safe-area-context": "^4.5.3", "react-native-screens": "^3.20.0", "react-native-share": "^11.0.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^13.9.0", "react-native-svg-transformer": "^1.0.0", "react-native-timeline-flatlist": "^0.8.0", "react-native-vector-icons": "^9.2.0", "react-native-virtualized-view": "^1.0.0", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-webview": "^13.2.1", "rn-wave-bottom-bar": "^2.2.36"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli": "latest", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/lodash": "^4.14.195", "@types/react": "^18.0.24", "@types/react-native-vector-icons": "^6.4.13", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.9", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}, "reactNativePermissionsIOS": ["Camera", "Notifications", "PhotoLibrary"]}