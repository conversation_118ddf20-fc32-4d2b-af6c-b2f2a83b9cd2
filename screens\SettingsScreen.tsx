import { useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  BackHandler,
  Platform,
  Share,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { requestNotifications } from 'react-native-permissions';
import WebView from 'react-native-webview';
import ToggleSwitch from '../common/ToggleSwitch';
import ConfirmModal from '../components/ConfirmModal';
import DeleteAccountModal from '../components/DeleteAccountModal';
import EnrollMFAModal from '../components/EnrollMFAModal';
import ErrorMessage from '../components/ErrorMessage';
import WarningModal from '../components/WarningModal';
import { useApi } from '../context/ApiContext';
import { useAuth } from '../context/AuthContext';
import { useRefresh } from '../context/RefreshContex';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

export type UserSettings = {
  allowPushNotifications: boolean;
  allowEmailNotifications: boolean;
  useDarkTheme: boolean;
  isMFAEnabled: boolean;
};

export type ExternalLinks = {
  moreInformation: string;
  privacyPolicy: string;
  playStore: string;
  appStore: string;
  shareAppText: string;
};

type ArchiveScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Archive'
>;

type Props = {
  navigation: ArchiveScreenNavigationProp;
};

const SettingsScreen: React.FC<Props> = ({navigation}) => {
  const [formData, setFormData] = useState<UserSettings>({
    allowEmailNotifications: false,
    allowPushNotifications: false,
    useDarkTheme: false,
    isMFAEnabled: false,
  });
  const [externalLinks, setExternalLinks] = useState<ExternalLinks>({
    moreInformation: '',
    privacyPolicy: '',
    playStore: '',
    appStore: '',
    shareAppText: '',
  });
  const [showPushNotifWarning, setShowPushNotifWarning] =
    useState<boolean>(false);
  const [showPushNotifQuestion, setShowPushNotifQuestion] =
    useState<boolean>(false);
  const [showEmailNotifQuestion, setShowEmailNotifQuestion] =
    useState<boolean>(false);
  const [showEnrollMFAModal, setShowEnrollMFAModal] = useState<boolean>(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] =
    useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [currentUrl, setCurrentUrl] = useState<string | null>(null);
  const {signout} = useAuth();
  const {themeStyles, toggleTheme, theme} = useTheme();
  const {refreshScreens} = useRefresh();
  const {getUserSettings, getExternalLinks, saveUserSetting} = useApi();
  const {isProviderLogin} = useAuth();

  useEffect(() => {
    setIsLoading(true);
    fetchSettings();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchSettings();
  };

  const fetchSettings = () => {
    getUserSettings()
      .then(result => {
        setFormData({
          ...formData,
          allowPushNotifications: result.allowPushNotifications,
          allowEmailNotifications: result.allowEmailNotifications,
          isMFAEnabled: result.isMFAEnabled,
        });

        setIsError(false);
      })
      .catch(error => {
        setIsError(true);
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
        setRefreshing(false);
      });

    getExternalLinks()
      .then(result => {
        if (result) {
          setExternalLinks(result);
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
        setRefreshing(false);
      });
  };
  useFocusEffect(
    React.useCallback(() => {
      setCurrentUrl(null);
    }, []),
  );
  const saveUserSettings = (name: string, value: any) => {
    saveUserSetting(name, value)
      .then(() => {})
      .catch(error => {
        console.log(error);
      });
  };

  const togglePushNotifications = () => {
    setFormData({
      ...formData,
      allowPushNotifications: !formData.allowPushNotifications,
    });

    saveUserSettings(
      'allowPushNotifications',
      !formData.allowPushNotifications,
    );

    if (!formData.allowPushNotifications) {
      requestNotifications(['alert', 'badge']).then(({status, settings}) => {
        if (status != 'granted') {
          setShowPushNotifWarning(true);
        }
      });
    }
  };

  const toggleEmailNotifications = () => {
    setFormData({
      ...formData,
      allowEmailNotifications: !formData.allowEmailNotifications,
    });

    saveUserSettings(
      'allowEmailNotifications',
      !formData.allowEmailNotifications,
    );
  };

  const toggleIsMFAEnabled = () => {
    setShowEnrollMFAModal(false);

    setFormData({
      ...formData,
      isMFAEnabled: !formData.isMFAEnabled,
    });

    saveUserSettings('isMFAEnabled', !formData.isMFAEnabled);
  };

  const onTogglePushNotifications = () => {
    if (formData.allowPushNotifications) {
      setShowPushNotifQuestion(true);
    } else {
      togglePushNotifications();
    }
  };

  const onToggleEmailNotifications = () => {
    if (formData.allowEmailNotifications) {
      setShowEmailNotifQuestion(true);
    } else {
      toggleEmailNotifications();
    }
  };

  const onToggleEnableMFA = () => {
    if (formData.isMFAEnabled) {
      toggleIsMFAEnabled();
    } else {
      setShowEnrollMFAModal(true);
    }
  };

  const onDisabledPushNotifications = () => {
    togglePushNotifications();

    setShowPushNotifQuestion(false);
  };

  const onDisabledEmailNotifications = () => {
    toggleEmailNotifications();

    setShowEmailNotifQuestion(false);
  };

  const onToggleTheme = () => {
    toggleTheme();
    refreshScreens(['Home']);
  };

  const viewArchived = () => {
    navigation.navigate('Archive');
  };
  const viewSupport= () => {
    navigation.navigate('Live Support');
  };
  const share = async () => {
    try {
      const storeLink =
        Platform.OS === 'android'
          ? externalLinks.playStore
          : externalLinks.appStore;

      const result = await Share.share(
        {
          message: storeLink,
          url: storeLink,
        },
        {
          dialogTitle: 'Flerts',
        }
      );
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const moreInfo = () => {
    setIsLoading(true)
    setCurrentUrl(externalLinks.moreInformation);
  };

  const privacyPolicy = () => {
    setIsLoading(true)
    setCurrentUrl(externalLinks.privacyPolicy);
  };

  // Handle hardware back button when WebView is open
  useEffect(() => {
    const backAction = () => {
      if (currentUrl) {
        setCurrentUrl('');
        return true; // Prevent default back action
      }
      return false; // Allow default back action
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [currentUrl]);

  return (
    <>
      <ConfirmModal
        visible={isModalVisible}
        question="Are you sure you want to log out?"
        onConfirm={signout}
        onReject={() => setIsModalVisible(false)}
      />
      {currentUrl ? (
        <View style={{flex: 1}}>
          {isLoading && (
            <ActivityIndicator
              style={styles.loader}
              size="large"
              color={themeStyles.primary}
            />
          )}

          <WebView
            source={{ uri: currentUrl }}
            style={{ flex: 1 }}
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
          />
        </View>
      ) : (
        <>
          {isLoading ? (
            <ActivityIndicator
              style={styles.loader}
              size="large"
              color={themeStyles.primary}
            />
          ) : !isError ? (
            <ScrollView
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
              style={[
                styles.container,
                {backgroundColor: themeStyles.background},
              ]}>
              <ConfirmModal
                visible={showPushNotifQuestion}
                question={
                  'Are you sure you want to disable push notifications?'
                }
                onConfirm={onDisabledPushNotifications}
                onReject={() => setShowPushNotifQuestion(false)}
              />
              <ConfirmModal
                visible={showEmailNotifQuestion}
                question={
                  'Are you sure you want to disable email notifications?'
                }
                onConfirm={onDisabledEmailNotifications}
                onReject={() => setShowEmailNotifQuestion(false)}
              />
              <WarningModal
                visible={showPushNotifWarning}
                warning={
                  'Flerts does not have permission to send push notifications, you can enable them in your device settings'
                }
                onConfirm={() => setShowPushNotifWarning(false)}
              />
              <EnrollMFAModal
                visible={showEnrollMFAModal}
                startScreen={isProviderLogin ? 'phone_number' : 'password'}
                onComplete={toggleIsMFAEnabled}
                onCancel={() => setShowEnrollMFAModal(false)}
              />
              <DeleteAccountModal
                visible={showDeleteAccountModal}
                onCancel={() => setShowDeleteAccountModal(false)}
              />
              <View style={styles.setting}>
                <Text style={{color: themeStyles.text}}>
                  Allow Push Notifications
                </Text>
                <ToggleSwitch
                  isEnabled={formData.allowPushNotifications}
                  toggleSwitch={onTogglePushNotifications}
                />
              </View>
              <View style={styles.setting}>
                <Text style={{color: themeStyles.text}}>
                  Allow Email Alerts
                </Text>
                <ToggleSwitch
                  isEnabled={formData.allowEmailNotifications}
                  toggleSwitch={onToggleEmailNotifications}
                />
              </View>
              <View style={styles.setting}>
                <Text style={{color: themeStyles.text}}>Dark Theme</Text>
                <ToggleSwitch
                  isEnabled={theme === 'dark'}
                  toggleSwitch={onToggleTheme}
                />
              </View>
              {/* <View style={styles.setting}>
                <Text style={{color: themeStyles.text}}>
                  Two Factor Authentication
                </Text>
                <ToggleSwitch
                  isEnabled={formData.isMFAEnabled}
                  toggleSwitch={onToggleEnableMFA}
                />
              </View> */}
              <View style={styles.settingButton}>
                <TouchableOpacity
                  style={[styles.btn, {backgroundColor: themeStyles.primary}]}
                  onPress={viewArchived}>
                  <Text
                    style={[styles.btnText, {color: themeStyles.background}]}>
                    Archive
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.settingButton}>
                <TouchableOpacity
                  style={[styles.btn, {backgroundColor: themeStyles.primary}]}
                  onPress={moreInfo}>
                  <Text
                    style={[styles.btnText, {color: themeStyles.background}]}>
                    More Information
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.settingButton}>
                <TouchableOpacity
                  style={[styles.btn, {backgroundColor: themeStyles.primary}]}
                  onPress={privacyPolicy}>
                  <Text
                    style={[styles.btnText, {color: themeStyles.background}]}>
                    Privacy policy
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.settingButton}>
                <TouchableOpacity
                  style={[styles.btn, {backgroundColor: themeStyles.primary}]}
                  onPress={share}>
                  <Text
                    style={[styles.btnText, {color: themeStyles.background}]}>
                    Share
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.settingButton}>
                <TouchableOpacity
                  style={[styles.btn, {backgroundColor: themeStyles.primary}]}
                  onPress={viewSupport}>
                  <Text
                    style={[styles.btnText, {color: themeStyles.background}]}>
                    Live Support
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.settingButton}>
                <TouchableOpacity
                  style={[
                    styles.logoutBtn,
                    {
                      backgroundColor: themeStyles.background,
                      borderColor: themeStyles.danger,
                    },
                  ]}
                  onPress={() => setIsModalVisible(true)}>
                  <Text
                    style={[styles.logoutText, {color: themeStyles.danger}]}>
                    Log out
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.settingButton}>
                <TouchableOpacity
                  style={[
                    styles.logoutBtn,
                    {
                      backgroundColor: themeStyles.background,
                      borderColor: themeStyles.danger,
                    },
                  ]}
                  onPress={() => setShowDeleteAccountModal(true)}>
                  <Text
                    style={[styles.logoutText, {color: themeStyles.danger}]}>
                    Delete Account
                  </Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          ) : (
            <ErrorMessage onRefresh={onRefresh} refreshing={refreshing} />
          )}
        </>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  setting: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 20,
  },
  settingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 20,
    paddingRight: 20,
    paddingBottom: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 10,
    borderRadius: 20,
  },
  btn: {
    marginTop: 10,
    marginBottom: 10,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  logoutBtn: {
    marginTop: 10,
    marginBottom: 10,
    width: '100%',
    padding: 12,
    height: 45,
  },
  logoutText: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    zIndex: 100,
  },
});

export default SettingsScreen;
