## Screenshots Naming Rules

Put all screenshots you want to use inside the folder of its language (e.g. `en-US`).
The device type will automatically be recognized using the image resolution.

The screenshots can be named whatever you want, but keep in mind they are sorted
alphabetically, in a human-friendly way. See https://github.com/fastlane/fastlane/pull/18200 for more details.

### Exceptions

#### iPad Pro (3rd Gen) 12.9"

Since iPad Pro (3rd Gen) 12.9" and iPad Pro (2nd Gen) 12.9" have the same image
resolution, screenshots of the iPad Pro (3rd gen) 12.9" must contain either the
string `iPad Pro (12.9-inch) (3rd generation)`, `IPAD_PRO_3GEN_129`, or `ipadPro129`
(App Store Connect's internal naming of the display family for the 3rd generation iPad Pro)
in its filename to be assigned the correct display family and to be uploaded to
the correct screenshot slot in your app's metadata.

### Other Platforms

#### Apple TV

Apple TV screenshots should be stored in a subdirectory named `appleTV` with language
folders inside of it.

#### iMessage

iMessage screenshots, like the Apple TV ones, should also be stored in a subdirectory
named `iMessage`, with language folders inside of it.
