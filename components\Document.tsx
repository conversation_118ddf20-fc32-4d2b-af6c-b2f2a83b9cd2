import { ActivityIndicator, Image, StyleSheet } from 'react-native'
import React, { useState, useEffect } from 'react'
import { useApi } from '../context/ApiContext';
import Center from './Center';

export default function Document({ documentName, width, height }: any) {

    const [blob, setBlob] = useState();
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isError, setIsError] = useState<boolean>(false);
    const { getDocument } = useApi();

    useEffect(() => {
        if (documentName) {
            if (documentName.includes("file:///")) {
                setBlob(documentName);
                setIsLoading(false);
            } else {
                setIsLoading(true);

                getDocument(documentName)
                    .then((result) => {
                        setBlob(result);
                        setIsError(false);
                    })
                    .catch((error) => {
                        console.log(error);
                        setIsError(true);
                    })
                    .finally(() => {
                        setIsLoading(false);
                    })
            }
        }
    }, [])

    return (
        !isLoading ? (
            <Image source={{ uri: blob }} style={{ ...styles.image, width: width, height: height }} resizeMode="contain" />
        ) : (
            <Center>
                <ActivityIndicator size="large" color="#108a00" />
            </Center>
        )
    )
}

const styles = StyleSheet.create({
    image: {
        margin: 5,
    },
})