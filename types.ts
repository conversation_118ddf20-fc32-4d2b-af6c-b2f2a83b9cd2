import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CategoryType } from './api/types/CategoryType';

export type RootStackParamList = {
  Login: undefined;
  SignedIn: undefined;
  SignUp: undefined;
  IntroSlider: undefined;
  Home: { refresh? : boolean };
  Alert: { categoryId?:string, subCategoryId?:string, alertId?: string, restore?: boolean };
  Expert:undefined;
  ForgotPassword:undefined;
  VerificationCode: undefined;
  Document:{alertId:string};
  SubCategories:{ categoryId: string, _subCategories?: CategoryType[]};
  Archive:undefined;
  Categories:{refresh:boolean};
  Category:undefined;
  SubCategory:{categoryId : string};
  EmailSent:{fromScreen : string};
  Profile: undefined;
};

type LoginScreenRouteProp = RouteProp<RootStackParamList, 'Login'>;
export type LoginScreenProps = {
  route: LoginScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'Login'>;
  navigationExpert: StackNavigationProp<RootStackParamList, 'Expert'>;
};

type SignedInScreenRouteProp = RouteProp<RootStackParamList, 'SignedIn'>;
export type SignedInScreenProps = {
  route: SignedInScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'SignedIn'>;
};

type SignUpScreenRouteProp = RouteProp<RootStackParamList, 'SignUp'>;
export type SignUpScreenProps = {
  route: SignUpScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'SignUp'>;
};

type ForgotPasswordScreenRouteProp = RouteProp<RootStackParamList, 'ForgotPassword'>;
export type ForgotPasswordScreenProps = {
  route: ForgotPasswordScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'ForgotPassword'>;
};

type IntroSliderScreenRouteProp = RouteProp<RootStackParamList, 'IntroSlider'>;
export type IntroSliderScreenProps = {
  route: IntroSliderScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'ForgotPassword'>;
  onCompleteSlider: () => void;
};

type VerificationCodeScreenRouteProp = RouteProp<RootStackParamList, 'VerificationCode'>;
export type VerificationCodeScreenRouteProps = {
  route: IntroSliderScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'VerificationCode'>;
};

type HomeScreenRouteProp = RouteProp<RootStackParamList, 'Home'>;
export type HomeScreenProps = {
  route: HomeScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'Home'>;
};

type AlertScreenRouteProp = RouteProp<RootStackParamList, 'Alert'>;
export type AlertScreenProps = {
  route: AlertScreenRouteProp;
  navigation: StackNavigationProp<RootStackParamList, 'Alert'>;
};

type ExpertScreenRouteProp = RouteProp<RootStackParamList, 'Expert'>;
export type ExpertScreenProps = {
  route: ExpertScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'Expert'>;
};

type DocumentDisplayScreenRouteProp = RouteProp<RootStackParamList, 'Document'>;
export type DocumentDisplayScreenProps = {
  route: DocumentDisplayScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'Document'>;
};

type SubCategoriesScreenRouteProp = RouteProp<RootStackParamList, 'SubCategories'>;
export type SubCategoriesScreenProps = {
  route: SubCategoriesScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'SubCategories'>;
};

type ArchiveScreenRouteProp = RouteProp<RootStackParamList, 'Archive'>;
export type ArchiveScreenProps = {
  route: ArchiveScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'Archive'>;
};

type CategoriesScreenRouteProp = RouteProp<RootStackParamList, 'Categories'>;
export type CategoriesScreenProps = {
  route: CategoriesScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'Categories'>;
};

type CategoryScreenRouteProp = RouteProp<RootStackParamList, 'Category'>;
export type CategoryScreenProps = {
  route: CategoryScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'Category'>;
};

type SubCategoryScreenRouteProp = RouteProp<RootStackParamList, 'SubCategory'>;
export type SubCategoryScreenProps = {
  route: SubCategoryScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'SubCategory'>;
};

type EmailSentScreenRouteProp = RouteProp<RootStackParamList, 'EmailSent'>;
export type EmailSentScreenProps = {
  route: EmailSentScreenRouteProp;
  navigationExpert: StackNavigationProp<RootStackParamList, 'EmailSent'>;
};

