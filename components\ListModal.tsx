import _ from 'lodash';
import React from 'react';
import { FlatList, Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { ListItem } from '../common/ListItem';
import { useTheme } from '../context/ThemeContext';

type Props = {
    dataList: ListItem[];
    visible: boolean;
    onClose: () => void;
    onItemSelect: (selectedItem: ListItem) => void;
    selectedItem?: string
};

const ListModal: React.FC<Props> = ({ dataList, visible, onClose, onItemSelect, selectedItem }) => {
    const { themeStyles } = useTheme();

    const sortedDataList = _.sortBy(dataList, 'name');

    const renderItem = ({ item }: { item: ListItem }) => (
        <TouchableOpacity
            key={item.id}
            style={[styles.listItem, selectedItem === item.id ? styles.selectedItem : null, { borderLeftColor: themeStyles.primary }]}
            onPress={() => {
                onItemSelect(item);
            }}
        >
            <Text style={[styles.listItemText, { color: themeStyles.text}]}>{item.name}</Text>
        </TouchableOpacity>
    );

    return (
        <Modal visible={visible} transparent>
            <View style={[styles.modalContainer, { backgroundColor: themeStyles.background }]}>
                <FlatList
                    data={sortedDataList}
                    renderItem={renderItem}
                    keyExtractor={(item) => item.id}
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                />
                <TouchableOpacity onPress={onClose} style={[styles.closeButton, { borderColor: themeStyles.text}]}>
                    <Text style={[styles.closeButtonText, { color: themeStyles.text }]}>Close</Text>
                </TouchableOpacity>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        paddingTop: 40,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    closeButton: {
        width: '90%',
        margin: 20,
        borderWidth: 2,
        padding: 12,
        borderRadius: 25,
        height: 45
    },
    closeButtonText: {
        fontWeight: "bold",
        textAlign: "center"
    },
    listItem: {
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderColor: '#ccc',
    },
    listItemText: {
        fontSize: 18,
    },
    selectedItem: {
        borderLeftWidth: 4
    },
});

export default ListModal;
