import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Mo<PERSON> from 'react-native-modal';
import { requestNotifications } from 'react-native-permissions';
import Timeline from 'react-native-timeline-flatlist';
import CheckingNotificationsIcon from '../assets/images/checking-notifications.svg';
import PlusIcon from '../assets/images/circle-plus-solid.svg';
import { AlertModel } from '../common/AlertModel';
import AlertListItem from '../components/AlertListItem';
import AlertModal from '../components/AlertModel/AlertModal';
import Center from '../components/Center';
import EnrollMFAModal from '../components/EnrollMFAModal';
import ErrorMessage from '../components/ErrorMessage';
import { useApi } from '../context/ApiContext';
import { useRefresh } from '../context/RefreshContex';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;
type Props = {
  navigation: HomeScreenNavigationProp;
  route: any;
};

// Memoized filter buttons component to prevent unnecessary re-renders
const FilterButtons = React.memo<{
  selectedFilter: string;
  themeStyles: any;
  onFilterChange: (filter: string) => void;
}>(({selectedFilter, themeStyles, onFilterChange}) => {
  const filters = useMemo(
    () => [
      {key: 'all', label: 'All'},
      {key: '7days', label: '7 Days'},
      {key: '30days', label: '30 Days'},
      {key: '90days', label: '90 Days'},
    ],
    [],
  );

  return (
    <View style={styles.filterContainer}>
      {filters.map(({key, label}) => (
        <TouchableOpacity
          key={key}
          style={[
            styles.filterButton,
            {borderColor: themeStyles.text},
            selectedFilter === key && {
              backgroundColor: themeStyles.primary,
              borderColor: themeStyles.primary,
            },
          ]}
          onPress={() => onFilterChange(key)}>
          <Text
            style={[
              styles.filterButtonText,
              {color: themeStyles.text},
              selectedFilter === key && {
                color: themeStyles.background,
                fontWeight: 'bold',
              },
            ]}>
            {label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
});

// Memoized modal component to prevent unnecessary re-renders
const WelcomeModal = React.memo<{
  isVisible: boolean;
  themeStyles: any;
  onClose: () => void;
}>(({isVisible, themeStyles, onClose}) => {
  const windowWidth = Dimensions.get('window').width;
  const iconSize = windowWidth * 0.4;

  return (
    <Modal
      isVisible={isVisible}
      style={[styles.modalContainer, {backgroundColor: themeStyles.background}]}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      backdropTransitionOutTiming={0}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.modalContentContainer}>
        <View
          style={{flex: 0.5, alignItems: 'center', justifyContent: 'center'}}>
          <Image
            source={require('../assets/images/logo-dark.png')}
            style={{
              width: iconSize,
              height: iconSize * 0.5,
              backgroundColor: themeStyles.background,
            }}
          />
        </View>
        <View style={styles.modalContent}>
          <CheckingNotificationsIcon
            width={iconSize}
            height={iconSize}
            fill={themeStyles.background}
          />
          <Text style={[styles.title, {color: themeStyles.text}]}>
            Welcome to Flerts
          </Text>
          <Text style={[styles.description, {color: themeStyles.text}]}>
            This app was designed for you to never miss a deadline.
          </Text>
          <Text style={[styles.description, {color: themeStyles.text}]}>
            Get expert help and switch plans and save money.
          </Text>
          <Text style={[styles.description, {color: themeStyles.text}]}>
            Turn on notifications for the full experience.
          </Text>
          <TouchableOpacity
            style={[styles.cancel, {borderColor: themeStyles.text}]}
            onPress={onClose}>
            <Text style={[styles.cancelText, {color: themeStyles.text}]}>
              Close
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Modal>
  );
});

const HomeScreen: React.FC<Props> = ({navigation, route}) => {
  const {themeStyles, theme} = useTheme();
  const {home, clearHome} = useRefresh();

  // State management
  const [refreshing, setRefreshing] = useState(false);
  const [timelineData, setTimelineData] = useState<AlertModel[]>([]);
  const [allAlertData, setAllAlertData] = useState<AlertModel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('all');

  // Modal states
  const [isModalVisible, setModalVisible] = useState(false);
  const [showNotificationsModal, setShowNotificationsModal] =
    useState<boolean>(false);
  const [showEnrollMFAModal, setShowEnrollMFAModal] = useState<boolean>(false);
  const [showPendingAlert, setShowPendingAlert] = useState<boolean>(false);
  const [pendingAlertData, setPendingAlertData] = useState<any>(null);

  // Alert states
  const [outstandingAlerts, setOutstandingAlerts] = useState<AlertModel[]>([]);
  const [categoryName, setCategoryName] = useState<string>('');
  const [alertModalAvailable, setAlertModalAvailable] = useState<boolean>(true);
  const [currentAlertIndex, setCurrentAlertIndex] = useState(0);
  const [showFeedImage, setShowFeedImage] = useState<boolean>(false);

  const {
    getAlerts,
    getOutstandingAlerts,
    getAboutMessage,
    registerDevice,
    hideAboutMessage,
    saveUserSetting,
  } = useApi();

  // Refs
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const lastUpdateRef = useRef<number>(0);

  // Memoized dimensions
  const dimensions = useMemo(() => {
    const windowWidth = Dimensions.get('window').width;
    return {
      iconSize: windowWidth * 0.4,
      plusIconSize: Math.min(windowWidth * 0.09, 65),
      plusIconButtonSize: Math.min(windowWidth * 0.13, 100),
      plusIconButtonBorderRadius: Math.min(windowWidth * 0.03, 30),
    };
  }, []);

  // Optimized time calculation function
  const calculateTimeLeft = useCallback((alert: any): string => {
    const now = Date.now();
    const expiryDate = new Date(alert.expiryDate).getTime();

    if (isNaN(expiryDate) || expiryDate < now) {
      return 'Expired';
    }

    const timeLeft = expiryDate - now;
    const days = Math.floor(timeLeft / 86400000); // 24 * 60 * 60 * 1000
    const hours = Math.floor((timeLeft % 86400000) / 3600000); // 60 * 60 * 1000
    const minutes = Math.floor((timeLeft % 3600000) / 60000); // 60 * 1000
    const seconds = Math.floor((timeLeft % 60000) / 1000);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} to go`;
    } else if (hours > 0) {
      return `${hours} ${hours > 1 ? 'hours' : 'hour'}`;
    } else if (minutes > 0) {
      return minutes < 5 && seconds > 0
        ? `${minutes} min ${seconds} sec`
        : `${minutes} min`;
    } else if (seconds > 0) {
      return `${seconds} sec`;
    }
    return 'Now';
  }, []);

  // Optimized countdown update with throttling
  const updateCountdown = useCallback(() => {
    const now = Date.now();
    // Throttle updates to every second
    if (now - lastUpdateRef.current < 1000) {return;}
    lastUpdateRef.current = now;

    if (!mountedRef.current) {return;}

    setTimelineData(prevData => {
      if (prevData.length === 0) {return prevData;}

      return prevData.map(alert => ({
        ...alert,
        time: calculateTimeLeft(alert),
      }));
    });
  }, [calculateTimeLeft]);

  // Optimized filter function with memoization
  const applyFilter = useCallback(
    (data: AlertModel[], filterType: string): AlertModel[] => {
      if (!data?.length) {return [];}

      const now = new Date();

      return data.filter(alert => {
        if (
          !alert.alertDate ||
          !alert.expiryDate ||
          alert.defaultAlertTime == null
        ) {
          return false;
        }

        const expiryDate = new Date(alert.expiryDate);
        if (expiryDate < now) {return false;}

        if (filterType === 'all') {return true;}

        const daysMatch = filterType.match(/^(\d+)days$/);
        if (!daysMatch) {return false;}

        const days = parseInt(daysMatch[1], 10);
        const endDate = new Date(now);
        endDate.setDate(endDate.getDate() + days);

        const nextAlertDate = new Date(alert.nextAlertDate);
        return (
          nextAlertDate >= now &&
          nextAlertDate <= endDate &&
          nextAlertDate <= expiryDate
        );
      });
    },
    [],
  );

  // Memoized timeline data processing
  const processTimelineData = useCallback(
    (data: AlertModel[], filter: string) => {
      const filteredData = applyFilter(data, filter);

      return filteredData.map(alert => {
        const color =
          alert.status === 0
            ? '#118C4F'
            : alert.status === 1
            ? '#ffb12b'
            : '#ff0000';

        return {
          ...alert,
          circleColor: color,
          lineColor: color,
          time: calculateTimeLeft(alert),
        };
      });
    },
    [applyFilter, calculateTimeLeft, themeStyles],
  );

  // Optimized data fetching
  const fetchData = useCallback(
    async (showLoading = true) => {
      if (showLoading) {
        setIsLoading(true);
        setTimelineData([]);
      }

      try {
        const newData = await getAlerts(false);

        if (!mountedRef.current) {return;}

        if (!newData?.length) {
          setTimelineData([]);
          setAllAlertData([]);
          setIsError(false);
          return;
        }

        setAllAlertData(newData);
        const processedData = processTimelineData(newData, selectedFilter);

        // Handle pending notification
        const pendingAlertId = await AsyncStorage.getItem('pendingAlertId');
        if (pendingAlertId) {
          const pendingAlert = processedData.find(
            alert => alert.id === pendingAlertId,
          );
          if (pendingAlert) {
            await AsyncStorage.removeItem('pendingAlertId');
            setPendingAlertData(pendingAlert);
            setShowPendingAlert(true);
          }
        }

        setTimelineData(processedData);
        setIsError(false);
      } catch (err) {
        if (mountedRef.current) {
          setError(err instanceof Error ? err.message : 'An error occurred');
          setIsError(true);
          setTimelineData([]);
        }
      } finally {
        if (mountedRef.current) {
          setIsLoading(false);
          setRefreshing(false);
        }
      }
    },
    [getAlerts, processTimelineData, selectedFilter],
  );

  // Optimized filter change handler
  const handleFilterChange = useCallback(
    (filterValue: string) => {
      if (filterValue === selectedFilter) {return;}

      setSelectedFilter(filterValue);
      setIsLoading(true);

      // Use requestAnimationFrame for smooth UI updates
      requestAnimationFrame(() => {
        const processedData = processTimelineData(allAlertData, filterValue);
        setTimelineData(processedData);
        setIsLoading(false);
      });
    },
    [selectedFilter, allAlertData, processTimelineData],
  );

  // Optimized outstanding alerts fetching
  const getOutStandingAlerts = useCallback(async () => {
    try {
      const result = await getOutstandingAlerts();

      if (!mountedRef.current || !result?.length) {
        setOutstandingAlerts([]);
        return;
      }

      const currentTime = Date.now();
      const validAlerts = result
        .filter((alert:AlertModel) => new Date(alert.nextAlertDate).getTime() >= currentTime)
        .sort(
          (a:AlertModel, b:AlertModel) =>
            new Date(a.nextAlertDate).getTime() -
            new Date(b.nextAlertDate).getTime(),
        );

      if (validAlerts.length > 0) {
        const alertData = timelineData.find(
          alert => alert.categoryId === validAlerts[0].categoryId,
        );
        if (alertData) {setCategoryName(alertData.categoryName);}
        setOutstandingAlerts(validAlerts);
        setCurrentAlertIndex(0);
      } else {
        setOutstandingAlerts([]);
      }
    } catch (error) {
      console.log('Error fetching outstanding alerts:', error);
    }
  }, [getOutstandingAlerts, timelineData]);

  // Setup notifications
  const setupNotifications = useCallback(async () => {
    try {
      const token = await messaging().getToken();
      await registerDevice(token);
    } catch (error) {
      console.log('Notification setup error:', error);
    }
  }, [registerDevice]);

  // Initialize app
  useEffect(() => {
    mountedRef.current = true;
    setIsLoading(true);

    const init = async () => {
      await Promise.all([setupNotifications(), fetchAboutMessage()]);
      await fetchData(false); // Don't show loading twice
    };

    init();

    // Setup interval for countdown
    intervalRef.current = setInterval(updateCountdown, 1000);

    return () => {
      mountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Handle refresh context
  useEffect(() => {
    if (home) {
      setIsLoading(true);
      Promise.all([fetchData(false), getOutStandingAlerts()]);
      clearHome();
    }
  }, [home, fetchData, getOutStandingAlerts, clearHome]);

  // Handle route changes
  useEffect(() => {
    setAlertModalAvailable(true);
  }, [route]);

  // Handle navigation focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchData(false);
    });
    return unsubscribe;
  }, [navigation, fetchData]);

  // Fetch about message
  const fetchAboutMessage = useCallback(async () => {
    try {
      const message = await getAboutMessage();
      const [isFirstTime, alreadyHideAboutMsg] = await Promise.all([
        AsyncStorage.getItem('SliderComplete'),
        AsyncStorage.getItem('hideAboutMessage'),
      ]);

      if (isFirstTime && !alreadyHideAboutMsg) {
        setModalVisible(true);
      } else {
        setModalVisible(message.showAboutMessage);
      }

      if (!message.showAboutMessage) {
        setShowNotificationsModal(
          message.loginCount % 5 === 0 && !message.allowPushNotifications,
        );
        await getOutStandingAlerts();
      }
    } catch (error) {
      console.log('About message error:', error);
      setModalVisible(false);
    }
  }, [getAboutMessage, getOutStandingAlerts]);

  // Event handlers
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    fetchData();
  }, [fetchData]);

  const addAlert = useCallback(() => {
    navigation.navigate('Alert', {});
  }, [navigation]);

  const actionAlert = useCallback(
    (id: string) => {
      const alertDB = timelineData.find(x => x.id === id);
      if (alertDB) {
        navigation.navigate('Alert', {alertId: id});
      }
    },
    [timelineData, navigation],
  );

  const renderDetail = useCallback(
    (data: any) => (
      <AlertListItem alert={data} onSelect={() => actionAlert(data.id)} />
    ),
    [actionAlert],
  );

  const toggleModal = useCallback(async () => {
    setModalVisible(false);
    await hideAboutMessage();
    requestNotifications(['alert', 'badge']);
    await AsyncStorage.setItem('onboardingComplete', 'false');
  }, [hideAboutMessage]);

  const onAlertCompleted = useCallback(
    (completedAlertId: string) => {
      const updatedAlerts = outstandingAlerts
        .filter(alert => alert.id !== completedAlertId)
        .sort(
          (a, b) =>
            new Date(a.nextAlertDate).getTime() -
            new Date(b.nextAlertDate).getTime(),
        );

      if (updatedAlerts.length > 0) {
        const alertData = timelineData.find(
          alert => alert.categoryId === updatedAlerts[0].categoryId,
        );
        if (alertData) {setCategoryName(alertData.categoryName);}
      }

      setOutstandingAlerts(updatedAlerts);
      setAlertModalAvailable(true);
      setCurrentAlertIndex(0);

      // Refresh data
      Promise.all([getOutStandingAlerts(), fetchData(false)]);
    },
    [outstandingAlerts, timelineData, getOutStandingAlerts, fetchData],
  );

  const onAlertActioned = useCallback(() => {
    if (showPendingAlert) {
      setShowPendingAlert(false);
      setPendingAlertData(null);
    } else {
      setAlertModalAvailable(false);
    }
  }, [showPendingAlert]);

  const toggleIsMFAEnabled = useCallback(async () => {
    setShowEnrollMFAModal(false);
    try {
      await saveUserSetting('isMFAEnabled', true);
    } catch (error) {
      console.log('MFA setting error:', error);
    }
  }, [saveUserSetting]);

  // Memoized computed values
  const shouldShowAlertModal = useMemo(
    () =>
      (outstandingAlerts.length > 0 &&
        alertModalAvailable &&
        currentAlertIndex < outstandingAlerts.length) ||
      showPendingAlert,
    [
      outstandingAlerts.length,
      alertModalAvailable,
      currentAlertIndex,
      showPendingAlert,
    ],
  );

  const currentAlert = useMemo(() => {
    if (showPendingAlert) {return pendingAlertData;}
    if (!shouldShowAlertModal) {return null;}

    return {
      ...outstandingAlerts[currentAlertIndex],
      categoryName:
        timelineData.find(
          t => t.categoryId === outstandingAlerts[currentAlertIndex].categoryId,
        )?.categoryName || categoryName,
    };
  }, [
    showPendingAlert,
    pendingAlertData,
    shouldShowAlertModal,
    outstandingAlerts,
    currentAlertIndex,
    timelineData,
    categoryName,
  ]);

  // Render methods
  const renderContent = () => {
    if (isLoading) {
      return (
        <Center style={{backgroundColor: themeStyles.background}}>
          <ActivityIndicator color={themeStyles.primary} size="large" />
        </Center>
      );
    }

    if (isError) {
      return <ErrorMessage onRefresh={handleRefresh} refreshing={refreshing} />;
    }

    if (timelineData.length > 0) {
      return (
        <Timeline
          style={styles.list}
          data={timelineData}
          timeContainerStyle={styles.timeContainerStyle}
          timeStyle={{
            textAlign: 'center',
            backgroundColor: themeStyles.background,
            color: themeStyles.text,
            padding: 5,
            borderRadius: 13,
            overflow: 'hidden',
            borderColor: themeStyles.text,
            borderWidth: 1,
          }}
          detailContainerStyle={{marginTop: 0}}
          listViewStyle={styles.listViewStyle}
          renderDetail={renderDetail}
          options={{
            showsVerticalScrollIndicator: false,
            showsHorizontalScrollIndicator: false,
            refreshControl: (
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
              />
            ),
          }}
        />
      );
    }

    return (
      <ScrollView
        contentContainerStyle={{flexGrow: 1, justifyContent: 'center'}}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        <Center>
          <Text
            style={{
              textAlign: 'center',
              paddingHorizontal: 20,
              color: themeStyles.text,
            }}>
            No alerts found for the selected filter. Try a different filter or
            tap the plus to create a new alert.
          </Text>
        </Center>
      </ScrollView>
    );
  };

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <WelcomeModal
        isVisible={isModalVisible}
        themeStyles={themeStyles}
        onClose={toggleModal}
      />

      <Modal
        isVisible={showNotificationsModal}
        style={[
          styles.modalContainer,
          {backgroundColor: themeStyles.background},
        ]}
        animationIn="slideInUp"
        animationOut="slideOutDown">
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.modalContentContainer}>
          <View style={styles.modalContent}>
            <CheckingNotificationsIcon
              width={dimensions.iconSize}
              height={dimensions.iconSize}
              fill={themeStyles.background}
            />
            <Text style={[styles.title, {color: themeStyles.text}]}>
              Enable Notifications
            </Text>
            <Text style={[styles.description, {color: themeStyles.text}]}>
              Get the most out of Flerts by enabling push notifications so you
              never miss an alert
            </Text>
            <TouchableOpacity
              style={[styles.btn, {borderColor: themeStyles.primary}]}
              onPress={() => navigation.navigate('Settings')}>
              <Text style={[styles.btnText, {color: themeStyles.background}]}>
                OK
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.cancel, {borderColor: themeStyles.text}]}
              onPress={() => setShowNotificationsModal(false)}>
              <Text style={[styles.cancelText, {color: themeStyles.text}]}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </Modal>

      <EnrollMFAModal
        visible={showEnrollMFAModal}
        onComplete={toggleIsMFAEnabled}
        onCancel={() => setShowEnrollMFAModal(false)}
        startScreen="recommend_mfa"
      />

      {shouldShowAlertModal && currentAlert && (
        <AlertModal
          navigation={navigation}
          visible={true}
          alert={currentAlert}
          alertCompleted={() => onAlertCompleted(currentAlert.id)}
          onAlertActioned={onAlertActioned}
          bypassTimeCheck={showPendingAlert}
        />
      )}

      <FilterButtons
        selectedFilter={selectedFilter}
        themeStyles={themeStyles}
        onFilterChange={handleFilterChange}
      />

      {renderContent()}

      {showFeedImage && (
        <Image
          source={require('../assets/images/urgent-tax.png')}
          style={styles.feedImage}
        />
      )}

      <TouchableOpacity
        style={[
          styles.plusIconButton,
          {
            backgroundColor: themeStyles.primary,
            width: dimensions.plusIconButtonSize,
            height: dimensions.plusIconButtonSize,
            borderRadius: dimensions.plusIconButtonBorderRadius,
          },
        ]}
        onPress={addAlert}>
        <PlusIcon
          width={dimensions.plusIconSize}
          height={dimensions.plusIconSize}
          fill={themeStyles.background}
          style={styles.raised}
        />
      </TouchableOpacity>
    </View>
  );
};

const {width} = Dimensions.get('window');
const dateContainerWidth = width < 800 ? 100 : 300;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingLeft: 20,
    paddingTop: 5,
    position: 'relative',
  },
  list: {
    flex: 1,
    marginTop: 10,
  },
  btn: {
    marginTop: 30,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cancel: {
    marginTop: 30,
    marginBottom: 15,
    width: '100%',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  cancelText: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#c5c5c5',
  },
  modalContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 15,
  },
  modalContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 25,
    marginBottom: 10,
  },
  description: {
    marginBottom: 10,
    textAlign: 'center',
  },
  plusIconButton: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 20,
    bottom: 30,
    zIndex: 1000,
    elevation: 5,
  },
  feedImage: {
    position: 'absolute',
    bottom: 0,
    width: undefined,
    height: '30%',
    aspectRatio: 1,
    transform: [{scaleX: -1}],
  },
  raised: {
    shadowColor: 'black',
    shadowRadius: 2,
    shadowOpacity: 0.8,
    shadowOffset: {width: 1, height: 1},
  },
  timeContainerStyle: {
    minWidth: dateContainerWidth,
    maxWidth: dateContainerWidth,
  },
  listViewStyle: {
    marginTop: 0,
  },
  filterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    paddingRight: 20,
    marginBottom: 5,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    minWidth: 70,
    alignItems: 'center',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default React.memo(HomeScreen);
