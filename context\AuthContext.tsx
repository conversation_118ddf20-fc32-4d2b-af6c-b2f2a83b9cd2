import { appleAuth } from '@invertase/react-native-apple-authentication';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import Config from 'react-native-config';

interface Props {
  children: React.ReactNode;
}
GoogleSignin.configure({
  webClientId: Config.WEB_CLIENT_ID,
  iosClientId: Config.IOS_CLIENT_ID
});
interface AuthContextType {
  initializing: boolean;
  accessToken: string | null;
  emailVerified: boolean;
  isProviderLogin: boolean;
  setProviderLogin(value: boolean): void;
  signin(email: string, password: string): Promise<void>;
  signout: () => void;
  signup(
    email: string,
    password: string,
    firstName: string,
    surname: string,
  ): Promise<void>;
  forgotPassword(email: string): Promise<void>;
  checkEmailVerified(): Promise<void>;
  enrollMFA: (phoneNumber: string) => Promise<string>;
  verifyNewMFA: (
    verificationId: string,
    verificationCode: string,
  ) => Promise<void>;
  resolveMFA(verificationCode: string): Promise<void>;
  confirmPassword(password: string): Promise<void>;
  resendEmailVerification(): Promise<void>;
  refreshAccessToken(): Promise<void>;
  signInWithGoogle(
    idToken: string,
    firstName: string,
    surname: string,
  ): Promise<void>;
  signInWithMicrosoft(): Promise<void>;
  handleAppleLogin(): Promise<void>
}

export type SignUpType = {
  firstname: string;
  surname: string;
};

const AuthContext = createContext<AuthContextType>({
  initializing: true,
  accessToken: null,
  emailVerified: true,
  isProviderLogin: false,
  setProviderLogin: (value: boolean) => { },
  signin: (email, password) => new Promise((resolve, reject) => { }),
  signout: () => { },
  signup: (email, password, firstName, surname) =>
    new Promise((resolve, reject) => { }),
  forgotPassword: email => new Promise((resolve, reject) => { }),
  checkEmailVerified: () => new Promise((resolve, reject) => { }),
  enrollMFA: async (phoneNumber: string) =>
    new Promise((resolve, reject) => { }),
  verifyNewMFA: async (verificationId: string, verificationCode: string) =>
    new Promise((resolve, reject) => { }),
  resolveMFA: (verificationCode: string) =>
    new Promise((resolve, reject) => { }),
  confirmPassword: (password: string) => new Promise((resolve, reject) => { }),
  resendEmailVerification: () => new Promise((resolve, reject) => { }),
  refreshAccessToken: () => new Promise((resolve, reject) => { }),
  signInWithGoogle: (idToken: string, firstName: string, lastName: string) =>
    new Promise((resolve, reject) => { }),
  signInWithMicrosoft: () => new Promise((resolve, reject) => { }),
  handleAppleLogin: () => new Promise((resolve, reject) => { }),
});

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  return context;
};

export const AuthProvider: React.FC<Props> = ({ children }) => {
  const [initializing, setInitializing] = useState<boolean>(true);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isProviderLogin, setIsProviderLogin] = useState<boolean>(false);
  const [emailVerified, setEmailVerified] = useState<boolean>(true);
  const [verificationId, setVerificationId] = useState<string>();
  const [resolver, setResolver] =
    useState<FirebaseAuthTypes.MultiFactorResolver>();


  const isFirstTimeSignUpRef = useRef<boolean>(false);
  const signUpDetailsRef = useRef<SignUpType | null>(null);
  async function createUser(
    token: string,
    data: SignUpType,
  ): Promise<string | null> {
    return new Promise((resolve, reject) => {
      fetch(`${Config.API_URL}CreateUser`, {
        method: 'post',
        headers: {
          Authorization: token,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
        .then(result => {
          if (result.status === 200) {
            resolve(null);
          } else {
            reject(`CreateUser returned ${result.status}`);
          }
        })
        .catch(error => {
          return reject('CreateUser: Request failed');
        });
    });
  }

  async function checkLocalProviderLogin(): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem('isProviderLogin');
      const status = value ? JSON.parse(value) : false;
      return Promise.resolve(status);
    } catch (error) {
      console.error(error);
      return Promise.reject(error);
    }
  }
  async function setLocalProviderLogin(value: boolean) {
    try {
      await AsyncStorage.setItem('isProviderLogin', `${value}`);
    } catch (error) {
      console.error(error);
    }
  }

  async function createUserAndSetIdToken(
    user: FirebaseAuthTypes.User,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      user.getIdToken().then(generatedToken => {
        if (signUpDetailsRef.current) {
          createUser(generatedToken, signUpDetailsRef.current)
            .then(() => {
              auth()
                .currentUser?.reload()
                .then(() => {
                  const currentUser = auth().currentUser;
                  currentUser
                    ?.getIdToken(true)
                    .then(token => {
                      setAccessToken(token);
                      setEmailVerified(true);
                    })
                    .catch(error => {
                      console.log(error);
                    });
                })
                .catch(error => {
                  console.log(error);
                });
              signUpDetailsRef.current = null;
              resolve();
            })
            .catch(error => {
              console.log("e", error);
              reject(error);
            });
        } else {
          reject();
        }
      }).catch((error) => {
        console.log("error from here", error);
      })
    });
  }

  useEffect(() => {
    checkLocalProviderLogin().then((value: boolean) => {
      setIsProviderLogin(value);
    });

    const subscriber = auth().onAuthStateChanged(userState => {
      if (userState) {
        userState
          .getIdToken()
          .then(newAccessToken => {
            setAccessToken(newAccessToken);
            if (isFirstTimeSignUpRef.current) {
              isFirstTimeSignUpRef.current = false;
              const isProvider = userState.providerData[0].providerId == "google.com" || userState.providerData[0]?.providerId == "apple.com" || userState.providerData[0]?.providerId == "microsoft.com"
              setEmailVerified(false);
              // Calling inside setProvider to always get the latest value of isProviderLogin
              if (!isProvider) userState.sendEmailVerification();
              setIsProviderLogin(isProvider);
              if (signUpDetailsRef.current) {
                createUser(newAccessToken, signUpDetailsRef.current)
                  .then(response => {
                    signUpDetailsRef.current = null;
                  })
                  .catch(error => {
                    signUpDetailsRef.current = null;
                  });
              }
            } else {
              setEmailVerified(userState.emailVerified);
            }
            setInitializing(false);
          })
          .catch(() => {
            signUpDetailsRef.current = null;
          });
      } else {
        setInitializing(false);
        setAccessToken(null);
      }
    });
    return subscriber;
  },[]);


  useEffect(() => {
    setLocalProviderLogin(isProviderLogin);
  }, [isProviderLogin]);

  return (
    <AuthContext.Provider
      value={{
        initializing: initializing,
        accessToken: accessToken,
        emailVerified: emailVerified,
        isProviderLogin: isProviderLogin,
        setProviderLogin(value) {
          setIsProviderLogin(value);
        },
        signin: (email, password) => {
          return new Promise((resolve, reject) => {
            auth()
              .signInWithEmailAndPassword(email, password)
              .then(() => {
                setIsProviderLogin(false);
                resolve();
              })
              .catch(error => {
                console.log(error);
                if (error.code === 'auth/multi-factor-auth-required') {
                  // This is where you would handle the multi-factor authentication
                  // const _resolver = auth().getMultiFactorResolver(error);
                  // const hint = _resolver.hints[0];
                  // const sessionId = _resolver.session;
                  // auth()
                  //   .verifyPhoneNumberWithMultiFactorInfo(hint, sessionId)
                  //   .then(verificationId => {
                  //     setVerificationId(verificationId);
                  //     setResolver(_resolver);
                  //     reject(error.code);
                  //   })
                  //   .catch(mfaError => {
                  //     if (mfaError.code == 'auth/popup-closed-by-user') {
                  //       reject(
                  //         'Recaptcha is required for multifactor authentication',
                  //       );
                  //     } else {
                  //       reject('Something went wrong');
                  //     }
                  //   });
                    reject('You already have a multi factor authentication set up. Please use the correct method to sign in.');
                } else if (
                  error.code == 'auth/wrong-password' ||
                  error.code == 'auth/user-not-found'
                ) {
                  reject('Incorrect username or password');
                } else if (error.code == 'auth/invalid-email') {
                  reject('Invalid email address');
                } else {
                  reject('Incorrect username or password');
                }
              });
          });
        },
        signout: async () => {
          try {
            const currentUser = auth().currentUser;
            if (currentUser) {
              await auth().signOut();
              if (isProviderLogin) {
                if (currentUser.providerData && currentUser.providerData[0]?.providerId == "google.com") {
                  await GoogleSignin.revokeAccess();
                  await GoogleSignin.signOut();
                }
                if (currentUser.providerData && currentUser.providerData[0]?.providerId == "apple.com") {
                await appleAuth.onCredentialRevoked(()=>{})
                }
                setIsProviderLogin(false);
              }
            }
          } catch (error) {
            console.log('Error signing out:', error);
          }
        },
        signup: (email, password, firstname, surname) => {
          signUpDetailsRef.current = { firstname: firstname, surname: surname };
          isFirstTimeSignUpRef.current = true;

          return new Promise((resolve, reject) => {
            auth()
              .createUserWithEmailAndPassword(email, password)
              .then(() => {
                resolve();
              })
              .catch(error => {
                signUpDetailsRef.current = null;
                isFirstTimeSignUpRef.current = false;
                if (error.code == 'auth/invalid-email') {
                  reject('Email address is invalid');
                } else if (error.code == 'auth/email-already-in-use') {
                  reject('Email address already in use');
                } else if (error.code == 'auth/weak-password') {
                  reject('Password not strong enough');
                } else {
                  reject('Something went wrong');
                }
              });
          });
        },
        forgotPassword: email => {
          return new Promise((resolve, reject) => {
            auth()
              .sendPasswordResetEmail(email)
              .then(() => {
                resolve();
              })
              .catch(error => {
                if (error.code == 'auth/invalid-email') {
                  reject('Email address is invalid');
                } else if (error.code == 'auth/email-already-in-use') {
                  reject('Email address already in use');
                } else if (error.code == 'auth/weak-password') {
                  reject('Password not strong enough');
                } else {
                  reject('Something went wrong');
                }
              });
          });
        },
        resendEmailVerification: () => {
          return new Promise((resolve, reject) => {
            auth()
              .currentUser?.sendEmailVerification()
              .then(() => {
                resolve();
              })
              .catch(error => {
                console.log(error);
                reject();
              });
          });
        },
        checkEmailVerified: () => {
          return new Promise((resolve, reject) => {
            auth()
              .currentUser?.reload()
              .then(() => {
                let user = auth().currentUser;

                if (user?.emailVerified == true) {
                  user
                    ?.getIdToken(true)
                    .then(token => {
                      setAccessToken(token);
                      setEmailVerified(true);
                      resolve();
                    })
                    .catch(error => {
                      console.log(error);
                      reject(error);
                    });
                }
                reject();
              })
              .catch(() => {
                reject();
              });
          });
        },
        enrollMFA: async (phoneNumber: string) => {
          const multiFactorUser = await auth().multiFactor(auth().currentUser!);
          const session = await multiFactorUser.getSession();

          const phoneOptions = { phoneNumber, session };
          return new Promise((resolve, reject) => {
            auth()
              .verifyPhoneNumberForMultiFactor(phoneOptions)
              .then(verificationId => resolve(verificationId))
              .catch(error => {
                if (error.code == 'auth/invalid-phone-number') {
                  reject('Phone number must be in the format +440123456789');
                }
                if (error.code == 'auth/popup-closed-by-user') {
                  reject(
                    'Recaptcha verification is required for multifactor authentication',
                  );
                } else {
                  console.log(error);
                  reject('Something went wrong');
                }
              });
          });
        },
        verifyNewMFA: async (
          verificationId: string,
          verificationCode: string,
        ) => {
          const cred = auth.PhoneAuthProvider.credential(
            verificationId,
            verificationCode,
          );
          const multiFactorAssertion =
            auth.PhoneMultiFactorGenerator.assertion(cred);

          return new Promise((resolve, reject) => {
            auth()
              .multiFactor(auth().currentUser!)
              .enroll(multiFactorAssertion)
              .then(() => resolve())
              .catch(error => {
                reject('Verification code is incorrect');
              });
          });
        },
        resolveMFA: (verificationCode: string) => {
          return new Promise((resolve, reject) => {
            if (verificationId != null) {
              const credential = auth.PhoneAuthProvider.credential(
                verificationId,
                verificationCode,
              );
              const multiFactorAssertion =
                auth.PhoneMultiFactorGenerator.assertion(credential);
              resolver
                ?.resolveSignIn(multiFactorAssertion)
                .then(userCredential => resolve())
                .catch(error => reject('Verification code is incorrect'));
            }
          });
        },
        confirmPassword: (password: string) => {
          var email = auth().currentUser?.email;

          return new Promise((resolve, reject) => {
            auth()
              .signInWithEmailAndPassword(email!, password)
              .then(() => {
                resolve();
              })
              .catch(error => {
                if (error.code == 'auth/multi-factor-auth-required') {
                  const resolver = auth().getMultiFactorResolver(error);
                } else {
                  reject('Incorrect password');
                }
              });
          });
        },
        refreshAccessToken: () => {
          return new Promise((resolve, reject) => {
            auth()
              .currentUser?.getIdToken(true)
              .then(newAccessToken => {
                setAccessToken(newAccessToken);
                resolve();
              })
              .catch(error => {
                reject(error);
              });
          });
        },
        signInWithGoogle: async(
          idToken: string,
          firstName: string,
          surname: string,
        ) => {
          signUpDetailsRef.current = {
            firstname: firstName,
            surname: surname,
          };
          return new Promise(async(resolve, reject) => {
            if (!idToken) {
              // If idToken is not present, the user likely canceled the sign-in
              reject(
                new Error(
                  'Google sign-in was canceled or no idToken returned.',
                ),
              );
              return;
            }
            const googleCredential =
              auth.GoogleAuthProvider.credential(idToken);
            auth()
              .signInWithCredential(googleCredential)
              .then(async resp => {
                if (
                  resp.additionalUserInfo &&
                  resp.additionalUserInfo.isNewUser
                ) {
                  isFirstTimeSignUpRef.current = true;
                } else {
                  isFirstTimeSignUpRef.current = false;
                }
                setIsProviderLogin(true);
                await AsyncStorage.setItem('hasLaunched', 'true');
                if (resp.user) {
                  createUserAndSetIdToken(resp.user)
                    .then(_ => resolve())
                    .catch(err => {
                      reject(err);
                    });
                  resolve();
                } else {
                  resolve();
                }
                resolve();
              })
              .catch(error => {
                console.log(error);
                if (auth().currentUser) {
                  auth().signOut();
                }
                reject();
              });
          });
        },
        signInWithMicrosoft: () => {
          return new Promise((resolve, reject) => {
            const provider = new auth.OAuthProvider('microsoft.com');
            (provider as any).setCustomParameters({
              prompt: 'login', // Forces the login screen to show up
              tenant: 'common',
            });
            auth()
              .signInWithRedirect(provider)
              .then(result => {
                if (result.user) {
                  setIsProviderLogin(true);
                  signUpDetailsRef.current = {
                    firstname: result.additionalUserInfo?.profile?.givenName,
                    surname: result.additionalUserInfo?.profile?.surname,
                  };
                  createUserAndSetIdToken(result.user)
                    .then(_ => resolve())
                    .catch(err => reject(err));
                  resolve();
                } else {
                  reject('No user found');
                }
              })
              .catch(error => {
                console.log(error);
                if (error.code === 'auth/web-context-canceled') {
                  if (auth().currentUser) {
                    auth().signOut();
                  }
                  reject();
                } else {
                  reject();
                  console.error(error);
                }
              });
          });
        },
        handleAppleLogin: async () => {
          return new Promise(async (resolve, reject) => {
            try {
              const appleAuthRequestResponse = await appleAuth.performRequest({
                requestedOperation: appleAuth.Operation.LOGIN,
                requestedScopes: [
                  appleAuth.Scope.EMAIL,
                  appleAuth.Scope.FULL_NAME
                ],
              });

              const {
                fullName,
                identityToken,
                nonce
              } = appleAuthRequestResponse;

              if (!identityToken) {
                throw new Error('Apple Sign-In failed - no identity token returned');
              }

              const appleCredential = await auth.AppleAuthProvider.credential(
                identityToken, nonce
              );
              const firebaseUserCredential = await auth().signInWithCredential(appleCredential);
              const isNewUser = firebaseUserCredential?.additionalUserInfo?.isNewUser;

              if (isNewUser) {
                isFirstTimeSignUpRef.current = true;
              } else {
                isFirstTimeSignUpRef.current = false;
              }
              if (fullName) {
                signUpDetailsRef.current = {
                  firstname: fullName.givenName || '',
                  surname: fullName.familyName || '',
                };
              } else {
                signUpDetailsRef.current = {
                  firstname: '',
                  surname: '',
                };
              }
              setIsProviderLogin(true);
              await AsyncStorage.setItem('hasLaunched', 'true');
              if (firebaseUserCredential.user) {
                await createUserAndSetIdToken(firebaseUserCredential.user); // Your backend registration
              }
              resolve();
            } catch (error:any) {
              if (auth().currentUser) {
                await auth().signOut();
              }
        
              // ✅ Cancelled by user — resolve silently instead of rejecting
              if (error?.code === appleAuth.Error.CANCELED) {
                console.log('Apple Sign-In cancelled by user.');
                return resolve(); // Do not reject on cancel
              }
        
              console.error('Apple Sign-In Error', error);
              reject(error); // Only reject non-cancel errors
            }
          });
        }
      }}>
      {children}
    </AuthContext.Provider>
  );
};
