import React, { useRef, useState } from 'react';
import { Dimensions, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useTheme } from '../context/ThemeContext';
import { Option } from './option';

interface CustomDropdownProps {
  options: Option[];
  onSelectOption: (option: Option) => void;
  placeholderText:string;
  background: string;
  textColor: string;
  style: object;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  onSelectOption,
  placeholderText,
  background,
  textColor,
  style
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);
  const dropdownRef = useRef<View>(null);
  const { themeStyles, theme } = useTheme();
  
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const selectOption = (option: Option) => {
    setSelectedOption(option);
    setIsOpen(false);
    onSelectOption(option);
  };

  const closeDropdown = () => {
    setIsOpen(false);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.input,
          {
            backgroundColor: themeStyles.background,
            borderColor: theme === 'dark' ? themeStyles.text : '#ccc',
          },
          style,
        ]}
        onPress={toggleDropdown}>
        <Text
          style={[
            styles.label,
            {
              color: themeStyles.text,
            },
          ]}>
          {selectedOption ? selectedOption.value : placeholderText}
        </Text>
      </TouchableOpacity>

      {isOpen && (
        <TouchableWithoutFeedback onPress={closeDropdown}>
          <View 
            style={[
              styles.dropdownContainer,
              {
                backgroundColor: themeStyles.background,
                borderColor: theme === 'dark' ? themeStyles.text : '#ccc',
              }
            ]} 
            ref={dropdownRef}>
            <ScrollView 
              style={styles.scrollView} 
              nestedScrollEnabled={true} 
              showsVerticalScrollIndicator={false} 
              showsHorizontalScrollIndicator={false}>
              {options.map(option => (
                <TouchableOpacity
                  key={option.Id}
                  onPress={() => selectOption(option)}
                  style={[
                    styles.optionContainer,
                    {borderBottomColor: theme === 'dark' ? themeStyles.text : '#ccc'}
                  ]}>
                  <Text 
                    style={[
                      styles.optionLabel,
                      {
                        color: themeStyles.text,
                        backgroundColor: 'transparent'
                      }
                    ]}>
                    {option.value}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
  },
  label: {
    fontSize: 13,
    textAlign: 'left',
    width: '100%',
    paddingLeft: 10,
    paddingVertical: 10,
  },
  optionContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  optionLabel: {
    padding: 15,
    fontSize: 13,
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 25,
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  dropdownContainer: {
    position: 'absolute',
    top: 45,
    left: 0,
    right: 0,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    zIndex: 9999,
    width: '100%',
    overflow: 'hidden',
  },
  scrollView: {
    maxHeight: Dimensions.get('window').height / 3,
  },
});

export default CustomDropdown;
