import {
    View,
    StyleSheet,
    TouchableOpacity,
    Text,
    TextInput,
    ActivityIndicator,
    Dimensions,
  } from 'react-native';
  import React, {useState} from 'react';
  import {useTheme} from '../../context/ThemeContext';
  import CheckIcon from '../../assets/images/check-solid.svg';
  import CrossIcon from '../../assets/images/x-solid.svg';
  import CalendarIcon from '../../assets/images/calendar-days-regular.svg';
  import DatePicker from 'react-native-date-picker';
  import {formatDate} from '../../common/helpers';
  import {useApi} from '../../context/ApiContext';

  export default function AlertModalSnooze({alert, alertCompleted, onBack,onAlertUpdate }: any) {
    const {theme, themeStyles} = useTheme();
    const {updateAlert, refreshBadgeNumber} = useApi();

    const inputIconSize = 16;
    const inputIconSizeNo = 15;
    const currentDate = new Date();
    const expiryDate = new Date(alert?.expiryDate || currentDate);
    const [date, setDate] = useState<Date>(new Date(new Date().getTime() + 60 * 1000));
    const [showPicker, setShowPicker] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const windowWidth = Dimensions.get('window').width;
    const inputWidth = windowWidth * 0.4;

    const formatDateTime = (date: Date) => {
      return `${formatDate(date)} ${date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      })}`;
    };

    const handleConfirm = (selectedDate: Date) => {
      setShowPicker(false);
      setDate(selectedDate);
    };

    const handleCancel = () => {
      setShowPicker(false);
    };

    const onConfirm = async () => {
        setIsLoading(true);
        try {
          const updatedAlertData = {
            ...alert,
            nextAlertDate: date.toISOString(),
          };
          await updateAlert(updatedAlertData);
          onAlertUpdate?.(updatedAlertData);
          refreshBadgeNumber();
          alertCompleted();
        } catch (error) {
          console.log('Error updating alert:', error);
        } finally {
          setIsLoading(false);
        }
      };

    return (
      <View style={[styles.modal, {backgroundColor: themeStyles.background}]}>
        <Text style={{paddingBottom: 10}}>When should you next be alerted?</Text>

        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <TextInput
            style={[
              styles.input,
              styles.center,
              {
                color: themeStyles.text,
                backgroundColor: themeStyles.background,
                width: inputWidth,
              },
            ]}
            value={formatDateTime(date)}
            editable={false}
          />
          <TouchableOpacity
            style={[
              styles.btn,
              styles.sideBySide,
              styles.btnDate,
              {backgroundColor: themeStyles.primary},
            ]}
            onPress={() => setShowPicker(true)}>
            <View style={styles.iconContainer}>
              <CalendarIcon
                width={inputIconSize}
                height={inputIconSize}
                fill={themeStyles.background}
              />
              <Text style={[styles.iconText, {color: themeStyles.background}]}>
                Date & Time
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        <DatePicker
          modal
          open={showPicker}
          date={date}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
          minimumDate={new Date(new Date().getTime() + 60 * 1000)}
          maximumDate={expiryDate}
          mode="datetime"
          theme={theme}
          title="Select Date and Time"
          confirmText="Confirm"
          cancelText="Cancel"
        />

        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[styles.button, {backgroundColor: themeStyles.primary}]}
            onPress={onConfirm}>
            <View style={styles.iconContainer}>
              {!isLoading ? (
                <>
                  <CheckIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={themeStyles.background}
                  />
                  <Text
                    style={[styles.confirmText, {color: themeStyles.background}]}>
                    OK
                  </Text>
                </>
              ) : (
                <ActivityIndicator size="small" color={themeStyles.background} />
              )}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.button,
              styles.reject,
              {borderColor: themeStyles.text},
            ]}
            onPress={() => onBack(1)}>
            <View style={styles.iconContainer}>
              <CrossIcon
                width={inputIconSizeNo}
                height={inputIconSizeNo}
                fill={themeStyles.text}
              />
              <Text style={[styles.rejectText, {color: themeStyles.text}]}>
                Back
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
const styles = StyleSheet.create({
  modal: {
    padding: 20,
    borderRadius: 8,
    width: '90%',
  },
  questionText: {
    fontSize: 16,
  },
  buttonsContainer: {
    marginTop: 15,
    flexDirection: 'column',
    justifyContent: 'space-evenly',
  },
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    padding: 10,
    borderRadius: 25,
    height: 40,
    marginBottom: 10,
  },
  confirmText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  reject: {
    borderWidth: 2,
  },
  rejectText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -2,
  },
  container: {
    flex: 1,
    zIndex: 1,
  },
  childContainer: {
    marginTop: 10,
    padding: 20,
  },
  groupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 2,
    marginBottom: 10,
    marginTop: 10,
  },
  label: {
    margin: 10,
    zIndex: -1,
  },
  inputDropdownLabel: {
    marginTop: 10,
    color: '#000',
  },
  inputDropdown: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    margin: 10,
    zIndex: -1,
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    marginTop: 10,
    zIndex: -1,
  },
  center: {
    textAlign: 'center',
  },
  sideBySide: {
    width: '45%',
  },
  inputNotes: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 5,
    margin: 10,
    minHeight: 100,
  },
  btnDate: {
    marginTop: 12,
  },
  btnSideBySide: {
    marginTop: 20,
    marginBottom: 15,
    width: '45%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 40,
    marginRight: 20,
  },
  btn: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 40,
  },
  btnDisabled: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#ccc',
    padding: 12,
    borderRadius: 25,
    height: 40,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  archive: {
    marginTop: 10,
    marginBottom: 15,
    width: '100%',
    borderColor: '#c5c5c5',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  categoryDropdown: {
    zIndex: 2,
  },
  subCategoryDropdown: {
    zIndex: 1,
  },
  iconText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
    fontSize: 12,
  },
  archiveIconText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  image: {
    width: 200,
    height: 200,
    margin: 5,
  },
  previewPhoto: {
    alignItems: 'center',
  },
  validationError: {
    marginTop: 5,
    marginLeft: 10,
    color: '#767676',
    fontWeight: 'bold',
  },
});
