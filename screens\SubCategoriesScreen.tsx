import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, FlatList, SafeAreaView, Dimensions, ActivityIndicator, RefreshControl, ScrollView } from 'react-native';
import { RootStackParamList } from '../types';
import { StackNavigationProp } from '@react-navigation/stack';
import { useRoute } from '@react-navigation/native';
import { useAuth } from '../context/AuthContext';
import { useApi } from '../context/ApiContext';
import { CategoryType } from '../api/types/CategoryType';
import IconDataList from '../common/IconDataList';
import PlusIcon from '../assets/images/circle-plus-solid.svg';
import ErrorMessage from '../components/ErrorMessage';
import Center from '../components/Center';
import { useTheme } from '../context/ThemeContext';
import SwipeableListItem from '../components/SwipeableListItem';
import ConfirmModal from '../components/ConfirmModal';
import { useRefresh } from '../context/RefreshContex';

type ItemProps = {
    item: CategoryType;
    onPress: () => void;
    backgroundColor: string;
    textColor: string;
};

type DeleteCategory = {
    categoryId: string,
    isCustomCategory: boolean
}

type AlertScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Alert'>;

type Props = {
    navigation: AlertScreenNavigationProp;
};

type RouteParams = {
    categoryId?: string;
    _subCategories?: CategoryType[];
};


const SubCategoryScreen: React.FC<Props> = ({ navigation }) => {

    const route = useRoute();
    const { categoryId, _subCategories }: RouteParams = route.params || {};
    const [selectedId, setSelectedId] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isError, setIsError] = useState<boolean>(false);
    const [subCategories, setSubCategories] = useState<CategoryType[]>([]);
    const [refreshing, setRefreshing] = useState<boolean>(false);
    const [categoryToDelete, setCategoryToDelete] = useState<DeleteCategory | null>(null);

    const { themeStyles } = useTheme();
    const { refreshScreens } = useRefresh();
    const { getSubCategories, deleteCategory } = useApi();

    const windowWidth = Dimensions.get('window').width;
    const inputIconSize = 32;
    const plusIconSize = Math.min(windowWidth * 0.09, 65);
    const plusIconButtonSize = Math.min(windowWidth * 0.13, 100);
    const plusIconButtonBorderRadius = Math.min(windowWidth * 0.03, 30);
    const iconDataList = new IconDataList();

    useEffect(() => {
        if (_subCategories && _subCategories.length > 0) {
            setSubCategories(_subCategories)
        } else {
            setIsLoading(true);
            fetchData();
        }
    }, [_subCategories]);

    const onRefresh = () => {
        setRefreshing(true);
        fetchData();
    }

    const fetchData = () => {
        if (categoryId && categoryId != "") {
            getSubCategories(categoryId, false)
                .then((result) => {
                    setSubCategories(result);
                    setIsError(false);
                })
                .catch((error) => {
                    console.log(error);
                    setIsError(true);
                })
                .finally(() => {
                    setIsLoading(false);
                    setRefreshing(false);
                });
        }
    }

    const renderItem = ({ item }: { item: CategoryType }) => {
        const displayAlertScreen = (item: CategoryType) => {
            setSelectedId(item.subCategoryId);
            navigation.navigate("Alert", { categoryId: item.categoryId, subCategoryId: item.subCategoryId });
        }

        item.icon = iconDataList.getIconByName(item.iconName);

        const Item = ({ item, onPress, backgroundColor, textColor }: ItemProps) => (
            <TouchableOpacity key={item.subCategoryId} onPress={onPress} style={{ ...styles.item, backgroundColor: backgroundColor }}>
                <View style={{ flexDirection: 'row' }}>
                    <View style={{ borderColor: '#ccc', borderRightWidth: 2, paddingRight: 10, justifyContent: 'center' }}>
                        {item.icon != null ?
                            <item.icon width={inputIconSize} height={inputIconSize} fill={themeStyles.primary} />
                            :
                            null
                        }
                    </View>
                    <View style={{ marginLeft: 10 }}>
                        <Text style={[styles.title, { color: themeStyles.primary }]}>{item.name}</Text>
                        <Text style={[styles.description, { color: themeStyles.text }]}>{item.description}</Text>
                    </View>
                </View>

            </TouchableOpacity>
        );

        return (
            <SwipeableListItem onDelete={() => setCategoryToDelete({ categoryId: item.subCategoryId, isCustomCategory: item.isCustomCategory })}>
                <Item
                    item={item}
                    onPress={() => displayAlertScreen(item)}
                    backgroundColor={themeStyles.background}
                    textColor={themeStyles.primary}
                />
            </SwipeableListItem>
        );
    };

    const addSubCategory = () => {
        if (categoryId != undefined) {
            navigation.navigate("SubCategory", { categoryId: categoryId });
        }

    }

    const onDelete = () => {
        setCategoryToDelete(null)
        if (categoryToDelete) {
            deleteCategory(categoryToDelete.categoryId, categoryToDelete?.isCustomCategory)
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    setRefreshing(true);
                    fetchData();
                    refreshScreens(["Home", "Documents"]);
                })
        }
    }

    return (
        <SafeAreaView style={styles.container}>
            <ConfirmModal visible={categoryToDelete != null} onConfirm={onDelete} onReject={() => setCategoryToDelete(null)} question="Are you sure you want to delete this subcategory? This will delete all alerts of this type, this action cannot be undone." />
            <View style={[styles.content, { backgroundColor: themeStyles.background }]}>
                {!isLoading ? (
                    !isError ? (
                        subCategories.length > 0 ? (
                            <FlatList
                                data={subCategories}
                                renderItem={renderItem}
                                keyExtractor={(item) => item.subCategoryId}
                                refreshControl={<RefreshControl onRefresh={onRefresh} refreshing={refreshing} />}
                            />) : (
                            <ScrollView refreshControl={<RefreshControl onRefresh={onRefresh} refreshing={refreshing} />} contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}>
                                <Center>
                                    <Text>No descriptions have been set</Text>
                                </Center>
                            </ScrollView>
                        )
                    ) : (
                        <ErrorMessage onRefresh={onRefresh} refreshing={refreshing} />
                    )
                ) : (
                    <Center>
                        <ActivityIndicator color={themeStyles.primary} size="large" />
                    </Center>
                )}
            </View>
            <View style={{ flexDirection: "row-reverse" }}>
                <TouchableOpacity style={[styles.plusIconButton, { backgroundColor: themeStyles.primary, marginRight: 20, width: plusIconButtonSize, height: plusIconButtonSize, borderRadius: plusIconButtonBorderRadius }]} onPress={() => addSubCategory()}>
                    <PlusIcon width={plusIconSize} height={plusIconSize} fill={themeStyles.background} style={styles.raised} />
                </TouchableOpacity>
            </View>
        </SafeAreaView>

    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },
    content: {
        flex: 1,
        padding: 16,
        backgroundColor: '#fff'
    },
    heading: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    item: {
        padding: 20,
        marginVertical: 5,
        marginHorizontal: 2,
        borderColor: '#767676',
        borderWidth: 1,
        borderRadius: 10
    },
    title: {
        fontSize: 18,
        color: '#108a00',
        paddingRight: 30,
    },
    description: {
        fontSize: 16,
        paddingRight: 30
    },
    plusIconButton: {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        alignSelf: 'center',
        marginRight: 20,
        bottom: 20
    },
    raised: {
        shadowColor: "black",
        shadowRadius: 2,
        shadowOpacity: 0.8,
        shadowOffset: { width: 1, height: 1 }
    }
});

export default SubCategoryScreen;