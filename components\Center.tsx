import { View, StyleSheet } from 'react-native'
import React from 'react'

type Props = {
    children: React.ReactNode;
}

export default function Center({ children }: Props) {
    return (
        <View style={styles.center}>
            {children}
        </View>
    )
}

const styles = StyleSheet.create({
    center: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
})