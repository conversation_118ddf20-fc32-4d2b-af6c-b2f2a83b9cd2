import React from 'react';
import { StyleSheet, View } from 'react-native';

type Props = {
    children: React.ReactNode;
    style?: object
}

export default function Center({ children,style }: Props) {
    return (
        <View style={[styles.center,style]}>
            {children}
        </View>
    )
}

const styles = StyleSheet.create({
    center: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
})