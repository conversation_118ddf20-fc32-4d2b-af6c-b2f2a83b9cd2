import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import React, { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { RootSiblingParent } from 'react-native-root-siblings';
import SplashScreen from 'react-native-splash-screen';
import Routing from './components/Routing';
import { ApiProvider } from './context/ApiContext';
import { AuthProvider } from './context/AuthContext';
import { RefreshProvider } from './context/RefreshContex';
import { ThemeProvider } from './context/ThemeContext';

function App(): JSX.Element {
    useEffect(() => {
        SplashScreen.hide();

        // Handle notification when app is opened from notification
        const handleNotificationOpen = async () => {
            // Check if app was opened from a notification
            const initialNotification = await messaging().getInitialNotification();
            if (initialNotification) {
                // Store the alert ID to be handled later when the app is fully loaded
                if (initialNotification.data?.alertId) {
                    await AsyncStorage.setItem('pendingAlertId', String(initialNotification.data.alertId));
                }
            }
        };

        // Handle notification when app is in background and user taps notification
        const unsubscribe = messaging().onNotificationOpenedApp(remoteMessage => {
            // Store the alert ID to be handled by HomeScreen
            if (remoteMessage.data?.alertId) {
                AsyncStorage.setItem('pendingAlertId', String(remoteMessage.data.alertId));
            }
        });

        handleNotificationOpen();

        return unsubscribe;
    }, []);

    return (
        <RootSiblingParent>
            <AuthProvider>
                <ThemeProvider>
                    <ApiProvider>
                        <GestureHandlerRootView style={{ flex: 1 }}>
                            <RefreshProvider>
                                <Routing />
                            </RefreshProvider>
                        </GestureHandlerRootView>
                    </ApiProvider>
                </ThemeProvider>
            </AuthProvider>
        </RootSiblingParent>
    );
}

export default App;
