import React, { JSXElementConstructor, useEffect, useState } from 'react';
import {
  Animated,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { AlertModel } from '../common/AlertModel';
import { formatDate } from '../common/helpers';
import IconDataList from '../common/IconDataList';
import { useTheme } from '../context/ThemeContext';

type Props = {
  alert: AlertModel;
  onSelect: () => void;
};

type Item = {
  icon: JSXElementConstructor<any> | null;
};

export default function AlertListItem({alert, onSelect}: any) {
  const {themeStyles,theme} = useTheme();

  const [animation, setAnimation] = useState(new Animated.Value(0));
  const [item, setItem] = useState<Item>({icon: null});
  const [displayName, setDisplayName] = useState<string>('');

  const iconDataList = new IconDataList();

  useEffect(() => {
    if (alert) {
      let icon = iconDataList.getIconByName(alert.iconName);
      setItem({icon: icon});
    }

    if (alert.companyName == '') {
      const displayNameParts = [
        alert.categoryName,
        alert.subCategoryName,
      ].filter(Boolean);
      setDisplayName(displayNameParts.join(' - '));
    } else {
      setDisplayName(alert.companyName);
    }
  }, [alert]);

  const onPressIn = () => {
    Animated.timing(animation, {
      toValue: 1,
      duration: 100,
      useNativeDriver: false,
    }).start();
  };

  let onPressOut = () => {
    Animated.timing(animation, {
      toValue: 0,
      duration: 50,
      useNativeDriver: false,
    }).start();
  };

  let inner = {
    elevation: animation.interpolate({
      inputRange: [0, 1],
      outputRange: [7, 0],
    }),
    shadowRadius: animation.interpolate({
      inputRange: [0, 1],
      outputRange: [5, 0],
    }),
  };

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background,}]}>
      <TouchableWithoutFeedback
        onPressIn={onPressIn}
        onPressOut={onPressOut}
        onPress={onSelect}>
        <View
          style={[styles.button, {
            backgroundColor: themeStyles.background, ...(theme === 'dark' && {
              borderColor: themeStyles.text,
              borderWidth: 1,
              borderRadius:10
            }),
}]}>
          <Animated.View
            style={[
              styles.inner,
              inner,
              {backgroundColor: themeStyles.background},
            ]}>
            <View
              style={{
                width: '100%',
                flex: 1,
                justifyContent: 'flex-start',
                flexDirection: 'row',
                alignItems: 'center',
                paddingTop: 15,
                paddingBottom: 15,
              }}>
              {item.icon != null && (
                <item.icon
                  width={30}
                  height={30}
                  fill={themeStyles.primary}
                  style={{marginLeft: 10, marginRight: 10}}
                />
              )}
              <View
                style={{
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  justifyContent: 'flex-start',
                  flexShrink: 1,
                }}>
                <Text style={[styles.policyNumber, {color: themeStyles.text}]}>
                  {displayName}
                </Text>
                <Text style={{color: themeStyles.text}}>
                  {formatDate(new Date(alert.alertDate))}
                </Text>
              </View>
            </View>
          </Animated.View>
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    paddingRight: 20,
    marginTop: -10,
  },
  button: {
    width: '100%',
  },
  inner: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingRight: 5,
    shadowRadius: 5,
    shadowColor: 'black',
    shadowOpacity: 0.25,
    shadowOffset: {width: 0, height: 0},
  },
  policyNumber: {
    fontWeight: 'bold',
    fontSize: 14,
    flex: 1,
  },
});
