import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState } from 'react';
import { ActivityIndicator, Dimensions, Image, KeyboardAvoidingView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

type VerificationCodeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'VerificationCode'>;

type Props = {
  navigation: VerificationCodeScreenNavigationProp;
};

const VerificationCodeScreen: React.FC<Props> = ({ navigation }) => {
  const { themeStyles,theme } = useTheme();
  const { resolveMFA } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [verificationCode, setVerificationCode] = useState<string>("");
  const [error, setError] = useState<string>("");

  const windowWidth = Dimensions.get('window').width;
  const iconSize = windowWidth * 0.4;

  const onVerify = () => {
    setIsLoading(true);

    resolveMFA(verificationCode)
      .then(() => {
      })
      .catch((error) => {
        setError(error);
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
      })
  };

  const onCancel = () => {
    navigation.navigate("Login");
  }

  return (
    <KeyboardAvoidingView
      style={[styles.background, {backgroundColor: themeStyles.background}]}
      behavior="padding">
      <ScrollView
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled">
        <View
          style={[styles.container, {backgroundColor: themeStyles.background}]}>
          <Image
            source={require('../assets/images/logo-dark.png')}
            style={{width: iconSize * 2, height: iconSize}}
          />
          {/* <NotificationIcon width={iconSize} height={iconSize} fill="#000" />
          <Text style={[styles.logo, { color: themeStyles.primary }]}>
            Flerts
          </Text> */}
          <Text style={{color: themeStyles.text}}>
            Enter the verifcation code
          </Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={[styles.input, {color: themeStyles.text}]}
              placeholder="Verification code"
              placeholderTextColor={themeStyles.text}
              onChangeText={value => setVerificationCode(value)}
              value={verificationCode}
              keyboardType="numeric"
            />
          </View>
          <TouchableOpacity
            style={[styles.signIn, {backgroundColor: themeStyles.primary}]}
            onPress={onVerify}>
            {!isLoading ? (
              <Text
                style={[styles.signInText, {color: themeStyles.background}]}>
                Verify
              </Text>
            ) : (
              <ActivityIndicator color="white" />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.cancel, styles.btnContainer]}
            onPress={onCancel}>
            <Text style={styles.cancelText}>Cancel</Text>
          </TouchableOpacity>
          {error && <Text style={{color: 'red'}}>{error}</Text>}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  background: {
    backgroundColor: '#fff',
    flex: 1
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  content: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  logo: {
    color: '#108a00',
    fontWeight: "bold",
    fontSize: 50
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 25,
    padding: 8,
    width: '100%',
    height: 45,
    marginTop: 20
  },
  input: {
    width: '90%',
    color: '#000',
    marginLeft: 10,
    height: 45,
  },
  icon: {
    position: 'absolute',
    top: 10,
    left: 10,
  },
  forgotPassword: {
    marginTop: 20,
    textAlign: "center"
  },
  signUpContainer: {
    marginTop: 30,
  },
  signUp: {
    marginTop: 30,
    textAlign: "center",
    color: '#108a00',
    fontWeight: "bold",
  },
  signIn: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 45
  },
  signInText: {
    color: '#fff',
    fontWeight: "bold",
    textAlign: "center"
  },
  notificationIconContainer: {
    alignSelf: 'center',
  },
  btnContainer: {
    marginBottom: 20
  },
  cancel: {
    marginTop: 10,
    marginBottom: 15,
    width: '100%',
    borderColor: '#c5c5c5',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45
  },
  cancelText: {
    color: '#767676',
    fontWeight: "bold",
    textAlign: "center"
  },
});

export default VerificationCodeScreen;