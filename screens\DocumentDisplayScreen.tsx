import { useRoute } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, View, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useApi } from '../context/ApiContext';
import { DocumentType } from '../api/types/DocumentType';
import Document from '../components/Document';
import Carousel from 'react-native-reanimated-carousel';
import Center from '../components/Center';
import ErrorMessage from '../components/ErrorMessage';
import { useTheme } from '../context/ThemeContext';

type RouteParams = {
    alertId?: string;
};

const DocumentDisplayScreen = ({ navigation }: any) => {

    const route = useRoute();
    const { alertId }: RouteParams = route.params || {};

    const { themeStyles } = useTheme();
    const { getDocuments } = useApi();

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isError, setIsError] = useState<boolean>(false);
    const [documents, setDocuments] = useState<DocumentType[]>([]);
    const [refreshing, setRefreshing] = useState<boolean>(false);

    const windowWidth = Dimensions.get('window').width;
    const windowHeight = Dimensions.get('window').height;

    useEffect(() => {
        setIsLoading(true);
        fetchData();
    }, [])

    const onRefresh = () => {
        setRefreshing(true);
        fetchData();
    }

    const fetchData = () => {
        if (alertId) {
            getDocuments(alertId)
                .then((result: DocumentType[]) => {
                    if (result.length == 0) {
                        navigation.navigate("Documents", { refresh: true });
                    }
                    setDocuments(result)
                    setIsError(false);
                })
                .catch((error) => {
                    console.log(error);
                    setIsError(true);
                })
                .finally(() => {
                    setIsLoading(false);
                    setRefreshing(false);
                })
        }
    }

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: themeStyles.background }}>
            {!isLoading ? (
                !isError ? (
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                        <Carousel
                            width={windowWidth * 0.95}
                            height={windowHeight * 0.9}
                            data={documents}
                            loop={false}
                            renderItem={({ item }) => (
                                <Document documentName={item.fileName} width={windowWidth} height={windowHeight} />
                            )}
                        />
                    </View>
                ) : (
                    <ErrorMessage onRefresh={onRefresh} refreshing={refreshing} />
                )
            ) : (
                <Center>
                    <ActivityIndicator size="large" color="#108a00" />
                </Center>
            )}
        </SafeAreaView>

    );
};

export default DocumentDisplayScreen;