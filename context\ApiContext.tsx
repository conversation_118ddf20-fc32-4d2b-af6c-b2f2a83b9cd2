import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext } from 'react';
import Config from 'react-native-config';
import { AlertModel } from '../common/AlertModel';
import { UserProfile } from '../common/UserModel';
import { ImageType as UploadFileType } from '../screens/AlertScreen';
import { QuestionType } from '../screens/ExpertScreen';
import { SignUpType, useAuth } from './AuthContext';

interface Props {
  children: React.ReactNode;
}

type ApiContextType = {
  getAboutMessage(): Promise<any>;
  hideAboutMessage(): Promise<any>;
  getAlerts(archived: boolean): Promise<any>;
  getAlertsWithDocuments(): Promise<any>;
  getAlert(id: string): Promise<any>;
  getOutstandingAlerts(): Promise<any>;
  createAlert(alert: AlertModel): Promise<any>;
  updateAlert(alert: AlertModel): Promise<any>;
  archiveAlert(alertId: string): Promise<any>;
  deleteAlert(alertId: string): Promise<any>;
  getCategories(isExpertAvailable: boolean): Promise<any>;
  getSubCategories(
    categoryKey: string,
    isExpertAvailable: boolean,
  ): Promise<any>;
  createCategory(category: any): Promise<any>;
  deleteCategory(categoryId: string, isCustomCategory: boolean): Promise<any>;
  createUser(data: SignUpType): Promise<any>;
  getUserSettings(): Promise<any>;
  saveUserSetting(name: string, value: any): Promise<any>;
  getDocuments(alertId: string): Promise<any>;
  saveDocuments(alertId: string, files: UploadFileType[]): Promise<any>;
  saveProfileImage(image: UploadFileType): Promise<boolean>;
  getDocument(documentName: string): Promise<any>;
  createQuestion(data: QuestionType): Promise<any>;
  registerDevice(token: string): Promise<any>;
  refreshBadgeNumber(): Promise<any>;
  getExternalLinks(): Promise<any>;
  updateUser(userObj: Omit<UserProfile, 'id'>): Promise<UserProfile>;
  deleteUser(): Promise<any>;
};

const ApiContext = createContext<ApiContextType>({
  getAboutMessage: () => new Promise((resolve, reject) => {}),
  hideAboutMessage: () => new Promise((resolve, reject) => {}),
  getAlerts: (archived: boolean) => new Promise((resolve, reject) => {}),
  getAlertsWithDocuments: () => new Promise((resolve, reject) => {}),
  getAlert: (id: string) => new Promise((resolve, reject) => {}),
  getOutstandingAlerts: () => new Promise((resolve, reject) => {}),
  createAlert: (alert: AlertModel) => new Promise((resolve, reject) => {}),
  updateAlert: (alert: AlertModel) => new Promise((resolve, reject) => {}),
  archiveAlert: (alertId: string) => new Promise((resolve, reject) => {}),
  deleteAlert: (alertId: string) => new Promise((resolve, reject) => {}),
  getCategories: (isExpertAvailable: boolean) =>
    new Promise((resolve, reject) => {}),
  getSubCategories: (categoryKey: string, isExpertAvailable: boolean) =>
    new Promise((resolve, reject) => {}),
  createCategory: (category: any) => new Promise((resolve, reject) => {}),
  deleteCategory: (categoryId: string, isCustomCategory: boolean) =>
    new Promise((resolve, reject) => {}),
  createUser: (data: SignUpType) => new Promise((resolve, reject) => {}),
  getUserSettings: () => new Promise((resolve, reject) => {}),
  saveUserSetting: (name: string, value: any) =>
    new Promise((resolve, reject) => {}),
  getDocuments: (alertId: string) => new Promise((resolve, reject) => {}),
  saveDocuments: (alertId: string, files: UploadFileType[]) =>
    new Promise((resolve, reject) => {}),
  saveProfileImage: (image: UploadFileType) =>
    new Promise<boolean>((resolve, reject) => {}),
  getDocument: (documentName: string) => new Promise((resolve, reject) => {}),
  createQuestion: (data: QuestionType) => new Promise((resolve, reject) => {}),
  registerDevice: (token: string) => new Promise((resolve, reject) => {}),
  refreshBadgeNumber: () => new Promise((resolve, reject) => {}),
  getExternalLinks: () => new Promise((resolve, reject) => {}),
  deleteUser: () => new Promise((resolve, reject) => {}),
  updateUser: (userObj: Omit<UserProfile, 'id'>) =>
    new Promise<UserProfile>((resolve, reject) => {}),
});

export const useApi = (): ApiContextType => {
  const context = useContext(ApiContext);
  return context;
};

export const ApiProvider: React.FC<Props> = ({children}) => {
  let {accessToken, refreshAccessToken, signout} = useAuth();

  const refreshToken = (callback: () => void) => {};

  

  return (
    <ApiContext.Provider
      value={{
        getAboutMessage: async function (): Promise<any> {
          return new Promise((resolve, reject) => {
            getAboutMessage();
            function getAboutMessage() {
              fetch(`${Config.API_URL}GetAboutMessage`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status === 200) {
                    result
                      .text()
                      .then(body => {
                        if (body) {
                          resolve(JSON.parse(body));
                        } else {
                          resolve(null);
                        }
                      })
                      .catch(err => {
                        reject('GetAboutMessage: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getAboutMessage);
                  } else {
                    reject(
                      `GetAboutMessage: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('GetAboutMessage: Request failed');
                });
            }
          });
        },
        hideAboutMessage: async function (): Promise<any> {
          return new Promise((resolve, reject) => {
            hideAboutMessage();

            function hideAboutMessage() {
              fetch(`${Config.API_URL}HideAboutMessage`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                    AsyncStorage.setItem('hideAboutMessage', 'true');
                  } else if (result.status == 401) {
                    refreshToken(hideAboutMessage);
                  } else {
                    reject(
                      `HideAboutMessage: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('HideAboutMessage: Request failed');
                });
            }
          });
        },
        getAlerts: async function (archived: boolean): Promise<any> {
          return new Promise((resolve, reject) => {
            getAlerts();

            function getAlerts() {
              fetch(`${Config.API_URL}GetAlerts?archived=${archived}`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('GetAlerts: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getAlerts);
                  } else {
                    result
                      .text()
                      .then(text =>
                        reject(
                          `GetAlerts: Request returned ${result.status} ${text}`,
                        ),
                      );
                    reject(`GetAlerts: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject(`GetAlerts: Request failed, ${error}`);
                });
            }
          });
        },
        getAlertsWithDocuments: async function (): Promise<any> {
          return new Promise((resolve, reject) => {
            getAlertsWithDocuments();

            function getAlertsWithDocuments() {
              fetch(`${Config.API_URL}GetAlertsWithDocuments`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('GetAlertsWithDocuments: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getAlertsWithDocuments);
                  } else {
                    reject(
                      `GetAlertsWithDocuments: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('GetAlertsWithDocuments: Request failed');
                });
            }
          });
        },
        getAlert: async function (id: string): Promise<any> {
          return new Promise((resolve, reject) => {
            getAlert();

            function getAlert() {
              fetch(`${Config.API_URL}GetAlert?id=${id}`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('GetAlert: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getAlert);
                  } else {
                    reject(`GetAlert: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('GetAlert: Request failed');
                });
            }
          });
        },
        getOutstandingAlerts: async function (): Promise<any> {
          return new Promise((resolve, reject) => {
            getOutstandingAlerts();

            function getOutstandingAlerts() {
              fetch(`${Config.API_URL}GetOutstandingAlerts`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('GetOustandingAlerts: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getOutstandingAlerts);
                  } else {
                    reject(
                      `GetOustandingAlerts: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('GetOustandingAlerts: Request failed');
                });
            }
          });
        },
        createAlert: async function (alert: AlertModel): Promise<any> {
          return new Promise((resolve, reject) => {
            createAlert();

            function createAlert() {
              fetch(`${Config.API_URL}CreateAlert`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(alert),
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('CreateAlert: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(createAlert);
                  } else {
                    reject(`CreateAlert: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('CreateAlert: Request failed');
                });
            }
          });
        },
        updateAlert: async function (alert: AlertModel): Promise<any> {
          return new Promise((resolve, reject) => {
            updateAlert();

            function updateAlert() {
              fetch(`${Config.API_URL}UpdateAlert`, {
                method: 'put',
                headers: {
                  Authorization: accessToken!,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(alert),
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('UpdateAlert: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(updateAlert);
                  } else {
                    reject(`UpdateAlert: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('UpdateAlert: Request failed');
                });
            }
          });
        },
        archiveAlert: async function (alertId: string): Promise<any> {
          return new Promise((resolve, reject) => {
            archiveAlert();

            function archiveAlert() {
              fetch(`${Config.API_URL}ArchiveAlert?alertId=${alertId}`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve('');
                  } else if (result.status == 401) {
                    refreshToken(archiveAlert);
                  } else {
                    reject(`UpdateAlert: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('UpdateAlert: Request failed');
                });
            }
          });
        },
        deleteAlert: async function (alertId: string): Promise<any> {
          return new Promise((resolve, reject) => {
            deleteAlert();

            function deleteAlert() {
              fetch(`${Config.API_URL}DeleteAlert?alertId=${alertId}`, {
                method: 'delete',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                  } else if (result.status == 401) {
                    refreshToken(deleteAlert);
                  } else {
                    reject(`DeleteAlert: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('DeleteAlert: Request failed');
                });
            }
          });
        },
        getCategories: async function (
          isExpertAvailable: boolean,
        ): Promise<any> {
          return new Promise((resolve, reject) => {
            getCategories();

            function getCategories() {
              fetch(
                `${Config.API_URL}GetCategories?isExpertAvailable=${isExpertAvailable}`,
                {
                  method: 'get',
                  headers: {
                    Authorization: accessToken!,
                  },
                },
              )
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('GetCategories: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getCategories);
                  } else {
                    reject(`GetCategories: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('GetCategories: Request failed');
                });
            }
          });
        },
        getSubCategories: async function (
          categoryKey: string,
          isExpertAvailable: boolean,
        ): Promise<any> {
          return new Promise((resolve, reject) => {
            getSubCategories();

            function getSubCategories() {
              fetch(
                `${Config.API_URL}GetSubCategories?categoryKey=${categoryKey}&isExpertAvailable=${isExpertAvailable}`,
                {
                  method: 'get',
                  headers: {
                    Authorization: accessToken!,
                  },
                },
              )
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('GetSubCategories: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getSubCategories);
                  } else {
                    reject(
                      `GetSubCategories: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('GetSubCategories: Request failed');
                });
            }
          });
        },
        createCategory: async function (category: any): Promise<any> {
          return new Promise((resolve, reject) => {
            createCategory();

            function createCategory() {
              fetch(`${Config.API_URL}CreateCustomCategory`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(category),
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('CreateCategory: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(createCategory);
                  } else {
                    reject(`CreateCategory: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('CreateCategory: Request failed');
                });
            }
          });
        },
        deleteCategory: async function (
          categoryId: string,
          isCustomCategory: boolean,
        ): Promise<any> {
          return new Promise((resolve, reject) => {
            deleteCategory();

            function deleteCategory() {
              fetch(
                `${Config.API_URL}DeleteCategory?categoryId=${categoryId}&isCustomCategory=${isCustomCategory}`,
                {
                  method: 'delete',
                  headers: {
                    Authorization: accessToken!,
                    'Content-Type': 'application/json',
                  },
                },
              )
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                  } else if (result.status == 401) {
                    refreshToken(deleteCategory);
                  } else {
                    reject(`DeleteCategory: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('DeleteCategory: Request failed');
                });
            }
          });
        },
        createUser: async function (data: SignUpType): Promise<string | null> {
          return new Promise((resolve, reject) => {
            createUser();

            function createUser() {
              fetch(`${Config.API_URL}CreateUser`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                  } else if (result.status == 401) {
                    refreshToken(createUser);
                  } else {
                    reject(`CreateUser returned ${result.status}`);
                  }
                })
                .catch(error => {
                  return reject('CreateUser: Request failed');
                });
            }
          });
        },
        getUserSettings: async function (): Promise<any> {
          return new Promise((resolve, reject) => {
            getUserSettings();

            function getUserSettings() {
              fetch(`${Config.API_URL}GetUserSettings`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .json()
                      .then(body => {
                        resolve(body);
                      })
                      .catch(err => {
                        reject('GetUserSettings: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    // refreshToken(getUserSettings).catch(err => {
                      reject('GetUserSettings: Token refresh failed');
                    // });
                  } else {
                    reject(
                      `GetUserSettings: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                   reject('GetUserSettings: Request failed');
                });
            }
          });
        },
        saveUserSetting: async function (
          name: string,
          value: any,
        ): Promise<any> {
          return new Promise((resolve, reject) => {
            saveUserSetting();

            function saveUserSetting() {
              fetch(`${Config.API_URL}UpdateUserSetting`, {
                method: 'patch',
                headers: {
                  Authorization: accessToken!,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify([
                  {op: 'add', path: `/${name}`, value: value},
                ]),
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve('');
                  } else if (result.status == 401) {
                    refreshToken(saveUserSetting);
                  } else {
                    reject(
                      `UpdateUserSetting: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('UpdateUserSetting: Request failed');
                });
            }
          });
        },
        getDocuments: async function (alertId: string): Promise<any> {
          return new Promise((resolve, reject) => {
            getDocuments();

            function getDocuments() {
              fetch(`${Config.API_URL}GetDocuments?alertId=${alertId}`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('GetDocuments: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getDocuments);
                  } else {
                    reject(`GetDocuments: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('GetDocuments: Request failed');
                });
            }
          });
        },
        saveDocuments: async function (
          alertId: string,
          files: UploadFileType[],
        ): Promise<any> {
          const formData = new FormData();

          files.forEach(file => {
            formData.append(file.id, {
              name: file.name,
              type: file.type,
              uri: file.uri,
            });
          });

          return new Promise((resolve, reject) => {
            if (files.length == 0) {
              resolve('');
            }

            saveDocuments();

            function saveDocuments() {
              fetch(`${Config.API_URL}SaveDocuments?alertId=${alertId}`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                },
                body: formData,
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve('');
                  } else if (result.status == 401) {
                    refreshToken(saveDocuments);
                  } else {
                    reject(
                      `UpdateUserSetting: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('Save Image: Request failed');
                });
            }
          });
        },
        saveProfileImage: async function (
          image: UploadFileType,
        ): Promise<boolean> {
          return new Promise((resolve, reject) => {
            if (!image) {
              resolve(false);
            }
            const formData = new FormData();

            formData.append(image.id, {
              name: image.name,
              type: image.type,
              uri: image.uri,
            });

            saveProfileImage();

            function saveProfileImage() {
              fetch(`${Config.API_URL}SaveProfile`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                },
                body: formData,
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(true);
                  } else if (result.status == 401) {
                    refreshToken(saveProfileImage);
                  } else {
                    reject(
                      `UpdateUserSetting: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  reject('Save Image: Request failed');
                });
            }
          });
        },
        getDocument: async function (documentName: string): Promise<any> {
          return new Promise((resolve, reject) => {
            getDocument();

            function getDocument() {
              fetch(`${Config.API_URL}Document?file=${documentName}`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .blob()
                      .then(blob => {
                        const fileReaderInstance = new FileReader();
                        fileReaderInstance.readAsDataURL(blob);
                        fileReaderInstance.onload = () => {
                          resolve(fileReaderInstance.result);
                        };
                      })
                      .catch(err => {
                        reject('Document: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getDocument);
                  } else {
                    reject(`Document: Request returned ${result.status}`);
                  }
                })
                .catch(error => {
                  reject('Document: Request failed');
                });
            }
          });
        },
        createQuestion: async function (
          data: QuestionType,
        ): Promise<string | null> {
          return new Promise((resolve, reject) => {
            createQuestion();

            function createQuestion() {
              fetch(`${Config.API_URL}CreateQuestion`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                  } else if (result.status == 401) {
                    refreshToken(createQuestion);
                  } else {
                    reject(`CreateQuestion returned ${result.status}`);
                  }
                })
                .catch(error => {
                  return reject('CreateQuestion: Request failed');
                });
            }
          });
        },
        registerDevice: async function (token: string): Promise<string | null> {
          return new Promise((resolve, reject) => {
            registerDevice();

            function registerDevice() {
              fetch(`${Config.API_URL}RegisterDevice`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({deviceToken: token}),
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                  } else if (result.status == 401) {
                    refreshToken(registerDevice);
                  } else {
                    reject(`RegisterDevice returned ${result.status}`);
                  }
                })
                .catch(error => {
                  return reject('RegisterDevice: Request failed');
                });
            }
          });
        },
        refreshBadgeNumber: async function (): Promise<string | null> {
          return new Promise((resolve, reject) => {
            refreshBadgeNumber();

            function refreshBadgeNumber() {
              fetch(`${Config.API_URL}RefreshBadgeNumber`, {
                method: 'post',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                  } else if (result.status == 401) {
                    refreshToken(refreshBadgeNumber);
                  } else {
                    reject(`RefreshBadgeNumber returned ${result.status}`);
                  }
                })
                .catch(error => {
                  return reject('RefreshBadgeNumber: Request failed');
                });
            }
          });
        },
        getExternalLinks: async function (): Promise<any> {
          return new Promise((resolve, reject) => {
            getExternalLinks();

            function getExternalLinks() {
              fetch(`${Config.API_URL}getExternalLinks`, {
                method: 'get',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    result
                      .text()
                      .then(body => {
                        resolve(JSON.parse(body));
                      })
                      .catch(err => {
                        reject('getExternalLinks: Failed reading body');
                      });
                  } else if (result.status == 401) {
                    refreshToken(getExternalLinks);
                  } else {
                    reject(
                      `getExternalLinks: Request returned ${result.status}`,
                    );
                  }
                })
                .catch(error => {
                  return reject('getExternalLinks: Request failed');
                });
            }
          });
        },
        updateUser: async function (
          userObj: Omit<UserProfile, 'id'>,
        ): Promise<UserProfile> {
          return new Promise((resolve, reject) => {
            updateUser();

            function updateUser() {
              fetch(`${Config.API_URL}UpdateUser`, {
                method: 'patch',
                headers: {
                  Authorization: accessToken!,
                },
                body: JSON.stringify(userObj),
              })
                .then(result => {
                  if (result.status == 200) {
                    result.json().then((body: UserProfile) => {
                      resolve(body);
                    });
                  } else if (result.status == 401) {
                    refreshToken(updateUser);
                  } else {
                    reject(`UpdateUser returned ${result.status}`);
                  }
                })
                .catch(error => {
                  return reject('UpdateUser: Request failed');
                });
            }
          });
        },
        deleteUser: async function (): Promise<string | null> {
          return new Promise((resolve, reject) => {
            deleteUser();

            function deleteUser() {
              fetch(`${Config.API_URL}DeleteUser`, {
                method: 'delete',
                headers: {
                  Authorization: accessToken!,
                },
              })
                .then(result => {
                  if (result.status == 200) {
                    resolve(null);
                  } else if (result.status == 401) {
                    refreshToken(deleteUser);
                  } else {
                    reject(`DeleteUser returned ${result.status}`);
                  }
                })
                .catch(error => {
                  return reject('DeleteUser: Request failed');
                });
            }
          });
        },
      }}>
      {children}
    </ApiContext.Provider>
  );
};
