import { RefreshControl, ScrollView, Text } from 'react-native'
import React from 'react'
import Center from './Center'
import { useTheme } from '../context/ThemeContext'
import { StyleSheet } from 'react-native';

export default function ErrorMessage({ onRefresh, refreshing }: any) {
    const { themeStyles } = useTheme();

    return (
        <ScrollView contentContainerStyle={styles.container} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}>               
            <Text style={{ color: themeStyles.text }}>Something went wrong</Text>   
        </ScrollView>
    )
}

const styles = StyleSheet.create({
    container: {
        flexGrow: 1,
        justifyContent: 'center',
        alignItems: 'center'
    },
});