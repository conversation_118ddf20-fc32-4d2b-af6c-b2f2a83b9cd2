import React from 'react';
import {
    ActivityIndicator,
    Modal,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import BackArrow from '../assets/images/arrow-left-solid.svg';
import CheckIcon from '../assets/images/check-solid.svg';
import { useApi } from '../context/ApiContext';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';

type Props = {
  visible: boolean;
  onCancel: () => void;
  startScreen?: Screen;
};

type Screen =
  | 'warning'
  | 'login_details'
  | 'verification_code'
  | 'confirm'
  | 'success';

export default function DeleteAccountModal({visible, onCancel}: Props) {
  const {themeStyles} = useTheme();
  const {signin, signout, resolveMFA, isProviderLogin} = useAuth();
  const {deleteUser} = useApi();

  const inputIconSizeYes = 16;
  const inputIconSizeNo = 15;

  const [screen, setScreen] = React.useState<Screen>('warning');
  const [email, setEmail] = React.useState<string>('');
  const [password, setPassword] = React.useState<string>('');
  const [verificationCode, setVerificationCode] = React.useState<string>('');
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string>('');

  const onBack = () => {
    setEmail('');
    setPassword('');
    setVerificationCode('');
    setScreen('warning');
    onCancel();
  };

  const onVerificationCodeChanged = (value: string) => {
    setVerificationCode(value);
  };

  const onFirstConfirm = () => {
    if (isProviderLogin) {
      onFinalConfirm();
    } else setScreen('login_details');
  };

  const onConfirmLoginDetails = () => {
    setIsLoading(true);
    if (email == '' || password == '') {
      setError('Please enter your email and password.');
      setIsLoading(false);
      return;
    }
    signin(email, password)
      .then(() => {
        setError('');
        setScreen('confirm');
      })
      .catch(error => {
        if (error == 'auth/multi-factor-auth-required') {
          setError('Contact support to delete your account.');
          // setScreen('verification_code');
        } else {
          setError(error);
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const onVerifyCode = () => {
    setIsLoading(true);
    resolveMFA(verificationCode)
      .then(() => {
        setError('');
        setScreen('confirm');
      })
      .catch(error => {
        setError(error);
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const onFinalConfirm = () => {
    setIsLoading(true);

    deleteUser()
      .then(() => {
        signout();
      })
      .catch(error => {
        console.log(error);
        setError('Something went wrong, please try again later.');
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <Modal visible={visible} transparent={true}>
      {screen == 'warning' && (
        <View style={styles.container}>
          <View
            style={[styles.modal, {backgroundColor: themeStyles.background}]}>
            <View>
              <Text style={[styles.questionText, {color: themeStyles.text}]}>
                This will delete all data associated with your account and you
                will not be able to log in to this account anymore, are you sure
                you want to delete your account?
              </Text>
            </View>
            <View style={[{flexDirection: 'column'}, styles.buttonsContainer]}>
              <TouchableOpacity
                style={[
                  styles.button,
                  {backgroundColor: themeStyles.danger},
                  {width: '100%'},
                ]}
                onPress={onFirstConfirm}>
                <View style={styles.iconContainer}>
                  <Text
                    style={[styles.confirmText, {color: themeStyles.white}]}>
                    Yes
                  </Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.reject,
                  {borderColor: themeStyles.text},
                  {width: '100%'},
                ]}
                onPress={onBack}>
                <View style={styles.iconContainer}>
                  <Text style={[styles.rejectText, {color: themeStyles.text}]}>
                    No
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
      {screen == 'login_details' && (
        <View style={styles.container}>
          <View
            style={[styles.modal, {backgroundColor: themeStyles.background}]}>
            <View>
              <Text style={[styles.questionText, {color: themeStyles.text}]}>
                Please confirm your email address and password to delete your
                account
              </Text>
            </View>
            <View style={[{flexDirection: 'column'}, styles.buttonsContainer]}>
              <TextInput
                placeholder="Email"
                placeholderTextColor={themeStyles.text}
                style={[
                  styles.input,
                  {
                    color: themeStyles.text,
                    backgroundColor: themeStyles.background,
                  },
                ]}
                onChangeText={value => setEmail(value)}
                value={email}
                textContentType="emailAddress"
              />
              <TextInput
                placeholder="Password"
                placeholderTextColor={themeStyles.text}
                style={[
                  styles.input,
                  {
                    color: themeStyles.text,
                    backgroundColor: themeStyles.background,
                  },
                ]}
                onChangeText={value => setPassword(value)}
                value={password}
                textContentType="password"
                secureTextEntry
              />
              {error != '' && <Text style={styles.error}>{error}</Text>}
              <TouchableOpacity
                style={[
                  styles.button,
                  {backgroundColor: themeStyles.danger},
                  {width: '100%'},
                ]}
                onPress={onConfirmLoginDetails}>
                <View style={styles.iconContainer}>
                  {isLoading ? (
                    <ActivityIndicator color={themeStyles.white} />
                  ) : (
                    <Text
                      style={[styles.confirmText, {color: themeStyles.white}]}>
                      Confirm
                    </Text>
                  )}
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.reject,
                  {borderColor: themeStyles.text},
                  {width: '100%'},
                ]}
                onPress={onBack}>
                <View style={styles.iconContainer}>
                  <BackArrow
                    width={inputIconSizeNo}
                    height={inputIconSizeNo}
                    fill={themeStyles.text}
                  />
                  <Text style={[styles.rejectText, {color: themeStyles.text}]}>
                    Back
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
      {screen == 'verification_code' && (
        <View style={styles.container}>
          <View
            style={[styles.modal, {backgroundColor: themeStyles.background}]}>
            <View>
              <Text style={[styles.questionText, {color: themeStyles.text}]}>
                Enter the code sent your phone number
              </Text>
            </View>
            <View style={[{flexDirection: 'column'}, styles.buttonsContainer]}>
              <TextInput
                placeholder="Verification code"
                placeholderTextColor={themeStyles.text}
                style={[
                  styles.input,
                  {
                    color: themeStyles.text,
                    backgroundColor: themeStyles.background,
                  },
                ]}
                onChangeText={onVerificationCodeChanged}
                value={verificationCode}
                keyboardType="numeric"
              />
              {error != '' && <Text style={styles.error}>{error}</Text>}
              <TouchableOpacity
                style={[
                  styles.button,
                  {backgroundColor: themeStyles.danger},
                  {width: '100%'},
                ]}
                onPress={onVerifyCode}>
                <View style={styles.iconContainer}>
                  {isLoading ? (
                    <ActivityIndicator color={themeStyles.white} />
                  ) : (
                    <>
                      <CheckIcon
                        width={inputIconSizeYes}
                        height={inputIconSizeYes}
                        fill={themeStyles.background}
                      />
                      <Text
                        style={[
                          styles.confirmText,
                          {color: themeStyles.background},
                        ]}>
                        Verify
                      </Text>
                    </>
                  )}
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.reject,
                  {borderColor: themeStyles.text},
                  {width: '100%'},
                ]}
                onPress={onBack}>
                <View style={styles.iconContainer}>
                  <BackArrow
                    width={inputIconSizeNo}
                    height={inputIconSizeNo}
                    fill={themeStyles.text}
                  />
                  <Text style={[styles.rejectText, {color: themeStyles.text}]}>
                    Back
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
      {screen == 'confirm' && (
        <View style={styles.container}>
          <View
            style={[styles.modal, {backgroundColor: themeStyles.background}]}>
            <View>
              <Text style={[styles.questionText, {color: themeStyles.text}]}>
                You data will not be recoverable, are you sure you want to
                delete your account?
              </Text>
            </View>
            <View style={[{flexDirection: 'column'}, styles.buttonsContainer]}>
              <TouchableOpacity
                style={[
                  styles.button,
                  {backgroundColor: themeStyles.danger},
                  {width: '100%'},
                ]}
                onPress={onFinalConfirm}>
                <View style={styles.iconContainer}>
                  <Text
                    style={[styles.confirmText, {color: themeStyles.white}]}>
                    Yes
                  </Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.reject,
                  {borderColor: themeStyles.text},
                  {width: '100%'},
                ]}
                onPress={onBack}>
                <View style={styles.iconContainer}>
                  <Text style={[styles.rejectText, {color: themeStyles.text}]}>
                    No
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    padding: 20,
    borderRadius: 8,
    width: '90%',
  },
  questionText: {
    fontSize: 16,
  },
  buttonsContainer: {
    marginTop: 15,
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  button: {
    padding: 10,
    borderRadius: 25,
    height: 40,
    marginBottom: 10,
  },
  confirmText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  reject: {
    borderWidth: 2,
  },
  rejectText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -2,
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    marginVertical: 10,
    zIndex: -1,
    width: '100%',
  },
  error: {
    textAlign: 'center',
    marginBottom: 5,
    marginLeft: 10,
    color: '#767676',
    fontWeight: 'bold',
  },
});
