import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';

interface ThemeContextType {
    theme: "light" | "dark";
    toggleTheme: () => void;
    themeStyles: ThemeStylesType
}

const ThemeContext = createContext<ThemeContextType>({
    theme: "light",
    toggleTheme: () => null,
    themeStyles: { primary: "", background: "", text: "", primaryDisabled: "", unselected: "", warning: "", danger: "", white: "", inputText:"" }
})

export const useTheme = (): ThemeContextType => {
    const context = useContext(ThemeContext);
    return context;
}

interface Props {
    children: React.ReactNode;
}

export const ThemeProvider: React.FC<Props> = ({ children }) => {
    const [theme, setTheme] = useState<"light" | "dark">("light");

    const toggleTheme = () => {
        let newTheme = theme === "light" ? "dark" : "light"
        
        //@ts-ignore
        setTheme(newTheme);

        try {
            AsyncStorage.setItem("Theme", newTheme);
        } catch (error) {
            console.log(error);
        }
    }

    const themeStyles = theme === "light" ? lightTheme : darkTheme;

    useEffect(() => {
        try {
            AsyncStorage.getItem("Theme")
            .then(result => {
                if (result) {
                    //@ts-ignore
                    setTheme(result);
                }
            })
        } catch (error) {
            console.log(error);
        }
    }, [])

    return (
        <ThemeContext.Provider value={{
            theme: theme,
            toggleTheme: toggleTheme,
            themeStyles: themeStyles
        }}>
            {children}
        </ThemeContext.Provider>
    )
}

interface ThemeStylesType {
  primary: string;
  background: string;
  text: string;
  primaryDisabled: string;
  unselected: string;
  warning: string;
  amber: string;
  danger: string;
  white: string;
  color: string;
  inputText: string;
}

const lightTheme : ThemeStylesType = {
    primary: "#118C4F", // 108a00
    background: "#ffffff",
    text: "#767676", // 767676,
    primaryDisabled: "#9cd6b9",
    unselected: "#c5c5c5",
    warning: "#ffb12b",
    amber: "#ffbf00",
    danger: "#ff0000",
    white: "#ffffff",
    color:"#ffffff",
    inputText:"#000"
}

const darkTheme : ThemeStylesType = {
    primary: "#ffffff",
    background: "#212121",
    text: "#ffffff",
    primaryDisabled: "#6a8075",
    unselected: "#707070",
    warning: "#ffb12b",
    danger: "#ff0000",
    white: "#ffffff",
    color:"grey",
    inputText: "#ffffff"
}