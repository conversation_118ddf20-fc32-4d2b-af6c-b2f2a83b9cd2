import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  useWindowDimensions,
  View,
} from 'react-native';
import CustomDropdown from '../common/CustomDropdown';
import IconDataItem from '../common/IconDataItem';
import IconDataList from '../common/IconDataList';
import {
  FieldValidation,
  ValidateField,
  ValidateForm,
} from '../common/Validation';
import { useApi } from '../context/ApiContext';
import { useRefresh } from '../context/RefreshContex';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

type CategoryScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Category'
>;

type Props = {
  navigation: CategoryScreenNavigationProp;
};

const CategoryScreen: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const {themeStyles,theme} = useTheme();
  const {refreshScreens} = useRefresh();
  const {createCategory} = useApi();

  // Use useWindowDimensions hook for responsive design
  const {width, height} = useWindowDimensions();

  // Calculate responsive sizes
  const isLandscape = width > height;
  const formPadding = width * 0.04; // 4% of screen width
  const inputHeight = Math.min(height * 0.06, 50); // 6% of height, max 50
  const buttonHeight = Math.min(height * 0.07, 55); // 7% of height, max 55
  const fontSize = Math.min(width * 0.04, 16); // 4% of width, max 16
  const iconSize = Math.min(width * 0.08, 32); // 6% of width, max 32

  // Calculate number of columns for icon grid based on screen width
  const numColumns = isLandscape ? 6 : 4;
  const iconButtonSize = Math.min(width * 0.16, 50); // 12% of width, max 60

  const iconDataList = new IconDataList();

  // Update layout when dimensions change
  useEffect(() => {
    // This effect will run when width or height changes
  }, [width, height]);

  const [form, setForm] = useState({
    name: '',
    description: '',
    selectedIcon: '',
    defaultAlertTime: '',
  });
  const [validation, setValidation] = useState({name: '', description: ''});
  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const defaultAlertTimeOptions = [
    {value: 'None', Id: '0'},
    {value: 'Weekly', Id: '1'},
    {value: 'Monthly', Id: '7'},
    {value: 'Annually', Id: '30'},
    {value: 'More Than Annually', Id: '90'},
  ];
  const validationSchema: FieldValidation[] = [
    {
      name: 'name',
      requirements: ['Required'],
      displayName: 'Name',
    },
    {
      name: 'description',
      requirements: ['Required'],
      displayName: 'Description',
    },
    {
      name: 'selectedIcon',
      requirements: ['Required'],
      displayName: 'Icon',
    },
  ];

  const onValueChange = (name: string, value: any) => {
    let newForm = {...form, [name]: value};
    setForm(newForm);
    setIsFormValid(ValidateForm(newForm, validationSchema));
  };

  const onFieldBlur = (name: string) => {
    let schema = validationSchema.filter(x => x.name === name);

    if (schema.length > 0) {
      //@ts-ignore
      let error = ValidateField(form, schema[0]);
      setValidation({...validation, [name]: error});
    }
  };

  const onCreateCategory = () => {
    setIsLoading(true);
    createCategory({
      name: form.name,
      description: form.description,
      iconName: form.selectedIcon,
      defaultAlertTime: form.defaultAlertTime ? form.defaultAlertTime.Id : '',
    })
      .then(() => {
        navigation.navigate('Categories', {refresh: true});
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
        refreshScreens(['Categories']);
      });
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const iconData = iconDataList.getIconData();

  const renderItem = ({item}: {item: IconDataItem}) => (
    <TouchableOpacity
      style={[
        styles.iconButton,
        {
          backgroundColor:
            form.selectedIcon === item.name
              ? themeStyles.primary
              : themeStyles.unselected,
          width: iconButtonSize,
          height: iconButtonSize,
        },
      ]}
      onPress={() => handleIconSelection(item)}>
      <item.icon
        width={iconSize}
        height={iconSize}
        fill={
          form.selectedIcon === item.name
            ? themeStyles.background // White color for selected icon
            : themeStyles.text
        }
      />
    </TouchableOpacity>
  );

  const handleIconSelection = (selectedIcon: IconDataItem) => {
    onValueChange('selectedIcon', selectedIcon.name);
  };

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <ScrollView
        style={[
          styles.container,
          {
            backgroundColor: themeStyles.background,
            padding: formPadding,
          },
        ]}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.inputContainer}>
          <Text
            style={[
              styles.label,
              {
                color: themeStyles.text,
                fontSize: fontSize,
              },
            ]}>
            Name <Text style={styles.mandatoryStar}>*</Text>
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeStyles.text,
                backgroundColor: themeStyles.background,
                height: inputHeight,
                fontSize: fontSize,
              },
            ]}
            onChangeText={text => onValueChange('name', text)}
            value={form.name}
            placeholder={form.name ? '' : 'i.e. Technology'}
            placeholderTextColor={theme === 'dark' ? '#fff' : 'gray'}
            onBlur={() => onFieldBlur('name')}
          />
          {validation.name !== '' && (
            <Text style={[styles.validationError, {fontSize: fontSize * 0.8}]}>
              {validation.name}
            </Text>
          )}
        </View>
        <View style={styles.dropdownContainer}>
          <Text
            style={[
              styles.label,
              {
                color: themeStyles.text,
                fontSize: fontSize,
              },
            ]}>
            Alert Frequency
          </Text>
          <CustomDropdown
            placeholderText="Alert Frequency"
            textColor={themeStyles.inputText}
            background={themeStyles.background}
            options={defaultAlertTimeOptions}
            onSelectOption={option => onValueChange('defaultAlertTime', option)}
            style={[styles.input, {height: inputHeight}]}
          />
        </View>
        <View style={styles.inputContainer}>
          <Text
            style={[
              styles.label,
              {
                color: themeStyles.text,
                fontSize: fontSize,
              },
            ]}>
            Description <Text style={styles.mandatoryStar}>*</Text>
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeStyles.text,
                backgroundColor: themeStyles.background,
                height: inputHeight,
                fontSize: fontSize,
              },
            ]}
            onChangeText={text => onValueChange('description', text)}
            placeholderTextColor={theme === 'dark' ? '#fff' : 'gray'}
            value={form.description}
            placeholder={form.description ? '' : 'i.e. software, and services'}
            onBlur={() => onFieldBlur('description')}
          />
          {validation.description !== '' && (
            <Text style={[styles.validationError, {fontSize: fontSize * 0.8}]}>
              {validation.description}
            </Text>
          )}
        </View>

        <View style={styles.iconContainer}>
          <Text
            style={[
              styles.label,
              {
                color: themeStyles.text,
                fontSize: fontSize,
              },
            ]}>
            Icon <Text style={styles.mandatoryStar}>*</Text>
          </Text>
          <FlatList
            data={iconData}
            renderItem={renderItem}
            keyExtractor={item => item.id.toString()}
            numColumns={numColumns}
            contentContainerStyle={styles.iconGrid}
            columnWrapperStyle={styles.iconRow}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            scrollEnabled={false}
          />
        </View>
        <TouchableOpacity
          style={
            isFormValid
              ? [
                  styles.btn,
                  {
                    backgroundColor: themeStyles.primary,
                    height: buttonHeight,
                  },
                ]
              : [
                  styles.btnDisabled,
                  {
                    backgroundColor: themeStyles.primaryDisabled,
                    height: buttonHeight,
                  },
                ]
          }
          onPress={onCreateCategory}
          disabled={!isFormValid}>
          {!isLoading ? (
            <Text
              style={[
                styles.btnText,
                {
                  color: themeStyles.background,
                  fontSize: fontSize,
                },
              ]}>
              Add
            </Text>
          ) : (
            <ActivityIndicator color={themeStyles.background} />
          )}
        </TouchableOpacity>
      </ScrollView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    paddingBottom: 10,
  },
  dropdownContainer: {
    zIndex: 1000,
    marginBottom: 10,
    marginTop: 0,
  },
  inputContainer: {
    marginBottom: 10,
    marginTop: 0,
  },
  iconContainer: {
    marginBottom: 10,
    marginTop: 0,
    width: '100%',
  },
  label: {
    color: '#767676',
    marginTop: 0,
    fontWeight: '500',
    marginBottom: 3,
  },
  input: {
    width: '100%',
    borderColor: '#ccc',
    borderWidth: 1,
    marginBottom: 5,
    paddingHorizontal: 10,
    // color: '#000',
    // backgroundColor: '#fff',
    borderRadius: 25,
    marginTop: 5,
  },
  btn: {
    marginTop: 15,
    marginBottom: 10,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
  },
  btnDisabled: {
    marginTop: 15,
    marginBottom: 10,
    width: '100%',
    backgroundColor: '#ccc',
    padding: 12,
    borderRadius: 25,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  iconGrid: {
    marginTop: 5,
    marginBottom: 5,
    width: '100%',
    paddingHorizontal: 0,
  },
  iconRow: {
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 6,
  },
  iconButton: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: 5,
    borderRadius: 10,
    backgroundColor: '#f2f2f2',
  },
  validationError: {
    marginTop: 0,
    marginBottom: 2,
    marginLeft: 10,
    color: '#767676',
    fontWeight: 'bold',
  },
  mandatoryStar: {
    color: 'red',
    fontWeight: 'bold',
  },
});

export default CategoryScreen;
