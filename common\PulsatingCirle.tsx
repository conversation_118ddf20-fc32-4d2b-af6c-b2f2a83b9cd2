import React, { useEffect, useRef } from 'react';
import { Animated, Easing, View } from 'react-native';

const PulsatingCircle = () => {
  const scaleValue = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const pulseAnimation = () => {
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.2,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ]).start(() => pulseAnimation());
    };

    pulseAnimation();
  }, []);

  return (
    <View style={{ flex: 1, marginLeft:10 }}>
      <Animated.View
        style={{
          width: 20,
          height: 20,
          borderRadius: 50,
          backgroundColor: 'red',
          transform: [{ scale: scaleValue }],
        }}
      />
    </View>
  );
};

export default PulsatingCircle;