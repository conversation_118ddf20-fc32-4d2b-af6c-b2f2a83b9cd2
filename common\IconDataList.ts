import { JSXElementConstructor } from "react";
import IconDataItem from "./IconDataItem";
import EmailIcon from "../assets/images/envelope-solid.svg"
import HomeIcon from "../assets/images/house-solid.svg"
import RoadIcon from '../assets/images/road-solid.svg';
import MotorbikeIcon from '../assets/images/motorcycle-solid.svg';
import ShoppingCartIcon from '../assets/images/cart-shopping-solid.svg';
import BabyIcon from '../assets/images/baby-solid.svg';
import StarIcon from '../assets/images/star-solid.svg';
import ShareIcon from '../assets/images/share-nodes-solid.svg';
import PhoneIcon from '../assets/images/phone-solid.svg';
import PlaneIcon from '../assets/images/plane-solid.svg';
import DumbbellIcon from '../assets/images/dumbbell-solid.svg';
import BicycleIcon from '../assets/images/bicycle-solid.svg';
import PeopleIcon from '../assets/images/people-group-solid.svg';
import CarIcon from '../assets/images/car-side-solid.svg';
import CalendarIcon from '../assets/images/calendar-days-regular.svg';
import MoneyIcon from '../assets/images/sterling-sign-solid.svg';
import TrainIcon from '../assets/images/train-solid.svg';
import HealthIcon from '../assets/images/user-nurse-solid.svg';
import FileIcon from '../assets/images/file-solid.svg';
import ArchiveIcon from '../assets/images/box-archive-solid.svg';
import FolderIcon from '../assets/images/folder-solid.svg';
import FolderOpenIcon from '../assets/images/folder-open-regular.svg';
import EnergyIcon from '../assets/images/lightbulb-solid.svg';
import AnimalIcon from '../assets/images/dog-solid.svg';
import TvIcon from '../assets/images/tv-solid.svg';

class IconDataList {
  private iconData: IconDataItem[];

  constructor() {
    this.iconData = [];
    this.iconData.push(new IconDataItem(1,"home",HomeIcon));
    this.iconData.push(new IconDataItem(2,"email",EmailIcon));
    this.iconData.push(new IconDataItem(3,"road",RoadIcon));
    this.iconData.push(new IconDataItem(4,"motorbike",MotorbikeIcon));
    this.iconData.push(new IconDataItem(5,"shopping",ShoppingCartIcon));
    this.iconData.push(new IconDataItem(6,"baby",BabyIcon));
    this.iconData.push(new IconDataItem(7,"star",StarIcon));
    this.iconData.push(new IconDataItem(8,"share",ShareIcon));
    this.iconData.push(new IconDataItem(9,"phone",PhoneIcon));
    this.iconData.push(new IconDataItem(10,"plane",PlaneIcon));
    this.iconData.push(new IconDataItem(11,"dumbell",DumbbellIcon));
    this.iconData.push(new IconDataItem(12,"bicyle",BicycleIcon));
    this.iconData.push(new IconDataItem(13,"people",PeopleIcon));
    this.iconData.push(new IconDataItem(14,"car",CarIcon));
    this.iconData.push(new IconDataItem(15,"calendar",CalendarIcon));
    this.iconData.push(new IconDataItem(16,"money",MoneyIcon));
    this.iconData.push(new IconDataItem(17,"train",TrainIcon));
    this.iconData.push(new IconDataItem(18,"health",HealthIcon));
    this.iconData.push(new IconDataItem(19,"file",FileIcon));
    this.iconData.push(new IconDataItem(20,"archive",ArchiveIcon));
    this.iconData.push(new IconDataItem(21,"folder",FolderIcon));
    this.iconData.push(new IconDataItem(22,"openfolder",FolderOpenIcon));
    this.iconData.push(new IconDataItem(23,"animal",AnimalIcon));
    this.iconData.push(new IconDataItem(24,"tv", TvIcon));
    this.iconData.push(new IconDataItem(25,"energy", EnergyIcon));
  }

  addIconDataItem(id: number,name:string, icon: JSXElementConstructor<any>) {
    this.iconData.push(new IconDataItem(id,name, icon));
  }

  getIconData(): IconDataItem[] {
    return this.iconData;
  }

  getIconByName(name: string): JSXElementConstructor<any> | null {
    const foundIcon = this.iconData.find((item) => item.name === name);
    return foundIcon ? foundIcon.icon : null;
  }

}

export default IconDataList;