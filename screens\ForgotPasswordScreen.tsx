import React, { useState } from 'react';
import { View, Text, Button, TouchableWithoutFeedback, StyleSheet, TextInput, TouchableOpacity, Keyboard, Dimensions } from 'react-native';
import { RootStackParamList } from '../types';
import { StackNavigationProp } from '@react-navigation/stack';
import EmailIcon from '../assets/images/envelope-solid.svg';
import { useAuth } from '../context/AuthContext';
import { IsValidEmail } from '../common/Validation';
import { useTheme } from '../context/ThemeContext';

type EmailSentScreenNavigationProp = StackNavigationProp<RootStackParamList, 'EmailSent'>;

type Props = {
    navigation: EmailSentScreenNavigationProp;
};

const ForgotPasswordScreen: React.FC<Props> = ({ navigation }) => {

    const { themeStyles } = useTheme();
    const [email, setEmail] = useState('');
    const [isValid, setIsValid] = useState<boolean>(false);
    const windowWidth = Dimensions.get('window').width;
    const inputIconSize = 24;
    const { forgotPassword } = useAuth();

    const onValueChange = (value: string) => {
        setIsValid(IsValidEmail(value));
        setEmail(value);
    }

    const handleCancel = () => {
        navigation.goBack();
    };

    const handleForgotPassword = () => {
        forgotPassword(email)
            .then(() => {
                navigation.navigate('EmailSent', { fromScreen: 'forgotPassword' });
            })
            .catch((error) => {
                console.log(error);
            })
    }

    const dismissKeyboard = () => {
        Keyboard.dismiss();
    };

    return (
        <TouchableWithoutFeedback onPress={() => dismissKeyboard()}>
            <View style={[styles.container, { backgroundColor: themeStyles.background }]}>
                <Text style={[styles.forgotText, { color: themeStyles.primary }]}>
                    Restore Password
                </Text>

                <View style={styles.inputContainer}>
                    <EmailIcon width={inputIconSize} height={inputIconSize} fill={themeStyles.text} style={{ marginLeft: 10 }} />
                    <TextInput
                        style={[styles.input, { color: themeStyles.text }]}
                        placeholder="Email"
                        placeholderTextColor={themeStyles.text}
                        onChangeText={text => onValueChange(text)}
                        value={email}
                        inputMode="email"
                        keyboardType="email-address"
                        textContentType="emailAddress"
                        autoCapitalize='none'
                    />
                </View>

                <Text style={[styles.text, { color: themeStyles.text }]}>
                    You will receive an email with a password reset link
                </Text>

                <TouchableOpacity style={isValid ? [styles.forgotPassword, { backgroundColor: themeStyles.primary }] : [styles.forgotPasswordDisabled, { backgroundColor: themeStyles.primaryDisabled }]} onPress={handleForgotPassword} disabled={!isValid}>
                    <Text style={styles.forgotPasswordText}>Send Instructions</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.cancel} onPress={handleCancel}>
                    <Text style={[styles.cancelText, { color: themeStyles.text }]}>Cancel</Text>
                </TouchableOpacity>
            </View>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
        backgroundColor: '#fff'
    },
    forgotText: {
        color: '#108a00',
        fontWeight: "bold",
        fontSize: 25
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 25,
        padding: 8,
        width: '100%',
        height: 45,
        marginTop: 20,
        marginBottom: 20

    },
    input: {
        width: '90%',
        color: '#000',
        marginLeft: 10,
        height: 45,
    },
    icon: {
        position: 'absolute',
        top: 10,
        left: 10,
    },
    forgotPassword: {
        marginTop: 20,
        marginBottom: 15,
        width: '100%',
        backgroundColor: '#108a00',
        padding: 12,
        borderRadius: 25,
        height: 45
    },
    forgotPasswordDisabled: {
        marginTop: 20,
        marginBottom: 15,
        width: '100%',
        backgroundColor: '#ccc',
        padding: 12,
        borderRadius: 25,
        height: 45
    },
    forgotPasswordText: {
        color: '#fff',
        fontWeight: "bold",
        textAlign: "center"
    },
    cancel: {
        marginTop: 10,
        marginBottom: 15,
        width: '100%',
        borderColor: '#c5c5c5',
        borderWidth: 2,
        padding: 12,
        borderRadius: 25,
        height: 45
    },
    cancelText: {
        color: '#767676',
        fontWeight: "bold",
        textAlign: "center"
    },
    text: {
        textAlign: "left"
    }
});

export default ForgotPasswordScreen;