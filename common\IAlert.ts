import { JSXElementConstructor } from 'react'

export type FileType = {
    id: string,
    fileName: string
}

export interface IAlert {
    id: string;
    categoryId:string;
    categoryName:string;
    subCategoryId:string;
    subCategoryName:string;
    time: string;
    isUrgent: boolean;
    policyNumber: string;
    companyName: string;
    expiryDate: Date;
    alertDate: Date;
    nextAlertDate: Date;
    defaultAlertTime: number;
    notes: string;
    circleColor: string;
    lineColor: string;
    documents: FileType[];
    isArchived: boolean;
    status: number;
    iconName: string;
    subCategoryIsRequired: boolean;
    showFeedImage: boolean;
}