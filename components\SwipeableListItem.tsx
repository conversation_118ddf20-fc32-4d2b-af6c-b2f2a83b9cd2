import { Animated, View, StyleSheet, TouchableOpacity, Text } from 'react-native'
import React from 'react'
import { Swipeable } from 'react-native-gesture-handler'
import TrashIcon from '../assets/images/trash-solid.svg';
import { Icon } from 'react-native-vector-icons/Icon';

export default function SwipeableListItem({ children, onDelete } : any) {
    
    
    const renderRightActions = (progress : any, dragX : any) => {
        const trans = progress.interpolate({
            inputRange: [0, 1],
            outputRange: [64, 0],
        });
        
        return (
            <View style={{ width: 120, flexDirection: 'row'}}>
                <Animated.View style={{ flex: 1, transform: [{ translateX: trans }] }}>
                    <TouchableOpacity style={styles.rightAction} onPress={onDelete}>
                        {/* <TrashIcon style={{ color: 'red', backgroundColor: 'white' }}/> */}
                        <Text style={{ color: 'red', fontSize: 15 }}>Delete</Text>
                    </TouchableOpacity>
                </Animated.View>
            </View>
        )
    }   

    return (
        //@ts-ignore
        <Swipeable rightThreshold={10} renderRightActions={renderRightActions}>
            {children}
        </Swipeable>
    )
}

const styles = StyleSheet.create({
    leftAction: {
      flex: 1,
      backgroundColor: '#497AFC',
      justifyContent: 'center',
    },
    actionText: {
      color: 'white',
      fontSize: 16,
      backgroundColor: 'transparent',
      padding: 0,
    },
    rightAction: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
      padding: 30
    },
  });