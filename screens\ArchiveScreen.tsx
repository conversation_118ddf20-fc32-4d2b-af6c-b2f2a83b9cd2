import {
  StyleSheet,
  SafeAreaView,
  View,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Text,
  Animated,
  Alert,
  Modal,
  Button,
} from 'react-native';
import _ from 'lodash';
import {useEffect, useState} from 'react';
import IconDataList from '../common/IconDataList';
import {AlertModel} from '../common/AlertModel';
import {formatDate} from '../common/helpers';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../types';
import {useApi} from '../context/ApiContext';
import {ActivityIndicator} from 'react-native';
import ConfirmModal from '../components/ConfirmModal';
import Center from '../components/Center';
import {useTheme} from '../context/ThemeContext';
import SwipeableListItem from '../components/SwipeableListItem';

type ItemProps = {
  item: AlertModel;
  onPress: () => void;
  backgroundColor: string;
  textColor: string;
};

type AlertScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Alert'
>;

type Props = {
  navigation: AlertScreenNavigationProp;
};

const ArchiveScreen: React.FC<Props> = ({navigation}) => {
  const {themeStyles} = useTheme();

  const {getAlerts, deleteAlert} = useApi();

  const [archivedAlerts, setArchivedAlerts] = useState<AlertModel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [alert, setAlertSelected] = useState<AlertModel>();
  const [showRestoreModal, setShowRestoreModal] = useState<boolean>(false);
  const [deleteArchiveId, setDeleteArchiveId] = useState<string>('');

  const windowWidth = Dimensions.get('window').width;
  const inputIconSize = windowWidth * 0.08;

  const iconDataList = new IconDataList();
  const [FolderIcons, setFolderIcons] = useState<any>();

  useEffect(() => {
    let folder = iconDataList.getIconByName('folder');
    let openFolder = iconDataList.getIconByName('openfolder');
    setFolderIcons({folder: folder, openFolder: openFolder});

    fetchData();
  }, []);

  const fetchData = () => {
    setIsLoading(true);
    getAlerts(true)
      .then(result => {
        let items = result.map((alert: AlertModel) => {
          return new AlertModel({
            id: alert.id,
            categoryId: alert.categoryId,
            categoryName: alert.categoryName,
            subCategoryId: alert.subCategoryId,
            subCategoryName: alert.subCategoryName,
            time: alert.time,
            isUrgent: alert.isUrgent,
            policyNumber: alert.policyNumber,
            companyName: alert.companyName,
            expiryDate: new Date(alert.expiryDate),
            alertDate: new Date(alert.alertDate),
            notes: alert.notes,
            circleColor: alert.circleColor,
            lineColor: alert.lineColor,
            documents: alert.documents,
            isArchived: alert.isArchived,
            status: alert.status,
            iconName: alert.iconName,
            nextAlertDate: alert.nextAlertDate,
            subCategoryIsRequired: false,
            showFeedImage: false,
          });
        });

        setArchivedAlerts(items);
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const onConfirmRestore = () => {
    if (selectedId) {
      navigation.navigate('Alert', {alertId: selectedId, restore: true});
      setShowRestoreModal(false);
    }
  };

  const onDelete = () => {
    deleteAlert(deleteArchiveId)
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setDeleteArchiveId('');
        fetchData();
      });
  };

  const renderItem = ({item}: {item: AlertModel}) => {
    const color =
      item.id === selectedId ? themeStyles.primary : themeStyles.text;

    const displayArchive = (item: AlertModel) => {
      if (selectedId == item.id) {
        setSelectedId(null);
        return;
      }

      setSelectedId(item.id);

      setAlertSelected(item);
    };

    const restoreAlert = (item: AlertModel) => {
      setShowRestoreModal(true);
    };

    const Item = ({item, onPress, backgroundColor, textColor}: ItemProps) => (
      <TouchableOpacity
        onPress={onPress}
        style={[styles.item, {backgroundColor}]}>
        <View style={{flexDirection: 'row'}}>
          <View
            style={{
              borderColor: '#ccc',
              borderRightWidth: 2,
              paddingRight: 10,
              justifyContent: 'center',
            }}>
            {FolderIcons.folder != null && selectedId != item.id && (
              <FolderIcons.folder
                width={inputIconSize}
                height={inputIconSize}
                fill={textColor}
              />
            )}
            {FolderIcons.openFolder != null && selectedId == item.id && (
              <FolderIcons.openFolder
                width={inputIconSize}
                height={inputIconSize}
                fill={textColor}
              />
            )}
          </View>
          <View style={{marginLeft: 10}}>
            {selectedId == item.id ? (
              <View>
                <View style={styles.alertDetail}>
                  <View>
                    <Text
                      style={[
                        styles.alertHeading,
                        {color: themeStyles.primary},
                      ]}>
                      Reference
                    </Text>
                    <Text>{alert?.policyNumber}</Text>
                    <Text
                      style={[
                        styles.alertHeading,
                        {color: themeStyles.primary},
                      ]}>
                      Category
                    </Text>
                    <Text>{alert?.categoryName}</Text>
                    {alert?.subCategoryName && (
                      <>
                        <Text
                          style={[
                            styles.alertHeading,
                            {color: themeStyles.primary},
                          ]}>
                          Description
                        </Text>
                        <Text>{alert?.subCategoryName}</Text>
                      </>
                    )}
                    <Text
                      style={[
                        styles.alertHeading,
                        {color: themeStyles.primary},
                      ]}>
                      Alert Date
                    </Text>
                    {alert?.alertDate != null && (
                      <Text>{formatDate(alert?.alertDate)}</Text>
                    )}
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <Text style={[styles.title, {color: themeStyles.text}]}>
                  {item.policyNumber}
                </Text>
                <Text style={[styles.description, {color: themeStyles.text}]}>
                  {item.companyName}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );

    return (
      <View>
        <ConfirmModal
          visible={deleteArchiveId != ''}
          onConfirm={onDelete}
          onReject={() => setDeleteArchiveId('')}
          question="Are you sure you want to delete this alert, this will delete all associated documents? This action cannot be undone."
        />
        <SwipeableListItem onDelete={() => setDeleteArchiveId(item.id)}>
          <Item
            item={item}
            onPress={() => displayArchive(item)}
            backgroundColor={themeStyles.background}
            textColor={color}
          />
        </SwipeableListItem>

        {selectedId == item.id ? (
          <TouchableOpacity
            style={[styles.btn, {backgroundColor: themeStyles.primary}]}
            onPress={() => restoreAlert(item)}>
            <Text style={[styles.btnText, {color: themeStyles.background}]}>
              Restore
            </Text>
          </TouchableOpacity>
        ) : null}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={[styles.content, {backgroundColor: themeStyles.background}]}>
        {!isLoading ? (
          archivedAlerts.length > 0 ? (
            <>
              <ConfirmModal
                visible={showRestoreModal}
                question={'Are you sure you want to restore this alert?'}
                onConfirm={onConfirmRestore}
                onReject={() => setShowRestoreModal(false)}
              />
              <FlatList
                data={archivedAlerts}
                renderItem={renderItem}
                keyExtractor={item => item.id.toString()}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
              />
            </>
          ) : (
            <Center>
              <Text
                style={{
                  textAlignVertical: 'center',
                  textAlign: 'center',
                  color: themeStyles.text,
                }}>
                Your archive is currently empty, if you archive an alert it will
                appear here
              </Text>
            </Center>
          )
        ) : (
          <Center>
            <ActivityIndicator
              style={styles.loader}
              size="large"
              color={themeStyles.primary}
            />
          </Center>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 10,
    backgroundColor: '#fff',
  },
  item: {
    padding: 20,
    marginVertical: 5,
    marginHorizontal: 2,
    borderColor: '#767676',
    borderWidth: 1,
    borderRadius: 10,
  },
  title: {
    fontSize: 18,
    color: '#767676',
  },
  description: {
    fontSize: 16,
  },
  alertHeading: {
    color: '#108a00',
    fontWeight: 'bold',
  },
  alertDetail: {
    flexDirection: 'row',
  },
  btn: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignContent: 'center',
  },
});

export default ArchiveScreen;
