import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, Easing, View } from 'react-native';
import WarningIcon from '../assets/images/warning.svg';

const BouncingImage: React.FC = () => {
  const translateYValue = useRef<Animated.Value>(new Animated.Value(0)).current;

  const windowWidth = Dimensions.get('window').width;
  const inputIconSize = windowWidth * 0.15; 
  
  useEffect(() => {
    const bounceAnimation = () => {
      Animated.sequence([
        Animated.timing(translateYValue, {
          toValue: -20,
          duration: 500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(translateYValue, {
          toValue: 0,
          duration: 500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ]).start(() => bounceAnimation());
    };

    bounceAnimation();
  }, []);

  return (
    <View style={{ flex: 1, marginLeft: 10 }}>
      <Animated.View
        style={{
          transform: [{ translateY: translateYValue }],
        }}
      >
      <WarningIcon width={inputIconSize} height={inputIconSize} fill="#000" />
    </Animated.View>
    </View>
  );
};

export default BouncingImage;
