import React from 'react';
import { SafeAreaView, View, Text, StyleSheet, Dimensions } from 'react-native';
import TidioChat from '../common/TidioChat';
import PlusIcon from '../assets/images/customer-service-svgrepo-com.svg';

const CustomerSupportPage = () => {
  return (
    <SafeAreaView style={styles.container}>
      {/* Content Section */}
      <View style={styles.content}>
      <View style={styles.textHeader}>
  <Text style={styles.welcomeText}>👋 Hey there, Welcome!</Text>
  <Text style={styles.mainHeading}>How can we assist you today?</Text>
  <View style={styles.underlineContainer}>
    <View style={styles.underline} />
  </View>
</View>



        <View style={styles.iconContainer}>
          <PlusIcon width={140} height={140} fill="#4e73df" />
        </View>

        <View style={styles.textContent}>
          <Text style={styles.description}>
            <Text style={styles.highlightText}>Have a burning question?</Text>{' '}
            <Text style={styles.highlightText}>Need expert help fast?</Text> Our expert support team is always ready and eager to assist you, anytime, anywhere. We're just one tap away.
          </Text>

          <View style={styles.divider} />

          <Text style={styles.note}>
            Don't wait! Tap the chat icon below and connect with our friendly experts instantly.
          </Text>
        </View>
      </View>

      {/* Tidio Chat Integration */}
      <TidioChat />
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8faff',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 25,
  },
  iconContainer: {
    backgroundColor: '#e8f0fe',
    borderRadius: 30,
    padding: 25,
    marginBottom: 35,
    shadowColor: '#4e73df',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 15,
    elevation: 8,
  },
  textContent: {
    alignItems: 'center',
    width: '100%',
  },
  description: {
    fontSize: 18,
    color: '#34495e',
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: width * 0.85,
    marginBottom: 25,
  },
  highlightText: {
    color: '#4e73df',
    fontWeight: '600',
  },
  divider: {
    height: 2,
    width: 60,
    backgroundColor: '#4e73df',
    opacity: 0.3,
    marginBottom: 25,
    borderRadius: 1,
  },
  supportInfo: {
    fontSize: 16,
    color: '#7f8c8d',
    marginBottom: 15,
    fontWeight: '500',
  },
  highlight: {
    color: '#27ae60',
    fontWeight: 'bold',
  },
  note: {
    fontSize: 15,
    color: '#95a5a6',
    textAlign: 'center',
    fontStyle: 'italic',
    maxWidth: width * 0.85,
    marginTop: 5,
    letterSpacing: 0.3,
  },
  textHeader: {
    alignItems: 'center',
    marginBottom: 25,
    paddingVertical: 15,
  },

  welcomeText: {
    fontSize: 22,
    fontWeight: '700',
    color: '#4e73df',
    letterSpacing: 1.2,
    textAlign: 'center',
  },

  mainHeading: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2c3e50',
    letterSpacing: 0.8,
    textAlign: 'center',
    marginTop: 5,
  },

  underlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    justifyContent: 'center',
  },

  underline: {
    width: 60,
    height: 4,
    backgroundColor: '#4e73df',
    borderRadius: 2,
    marginHorizontal: 3,
  },

  underlineAccent: {
    width: 25,
    height: 4,
    backgroundColor: '#27ae60',
    borderRadius: 2,
  },


});

export default CustomerSupportPage;
