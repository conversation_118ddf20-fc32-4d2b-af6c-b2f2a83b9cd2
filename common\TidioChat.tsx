import React, { useRef, useState, useEffect } from 'react';
import { View, StyleSheet, SafeAreaView, Dimensions, KeyboardAvoidingView, Platform, ActivityIndicator } from 'react-native';
import { WebView } from 'react-native-webview';

const TidioChat = () => {
  const webViewRef = useRef<WebView | null>(null);
  const [isWebViewLoaded, setIsWebViewLoaded] = useState(false);

  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <title>Tidio Chat</title>
        <style>
          html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: transparent;
          }
        </style>
      </head>
      <body>
        <script src="https://code.tidio.co/ua5aey1quwvptz3yqtvkbt9iun9ssp5w.js" async></script>
        <script>
        document.addEventListener("DOMContentLoaded", function () {
          var checkTidio = setInterval(function () {
            if (window.tidioChatApi) {
              clearInterval(checkTidio);
              window.ReactNativeWebView.postMessage("chatLoaded"); // Notify React Native that chat is ready
            }
          }, 500);

          window.onmessage = function (event) {
            if (event.data === "openChat" && window.tidioChatApi) {
              window.tidioChatApi.show();
              window.tidioChatApi.open();
            }
          };
        });
      </script>
      </body>
    </html>
  `;

  useEffect(() => {
    if (isWebViewLoaded && webViewRef.current) {
      setTimeout(() => {
        webViewRef.current?.postMessage("openChat");
      }, 100);
    }
  }, [isWebViewLoaded]);

  const onWebViewMessage = (event: any) => {
    if (event.nativeEvent.data === "chatLoaded") {
      setIsWebViewLoaded(true);
    }
  };

  const windowWidth = Dimensions.get('window').width;
  const windowHeight = Dimensions.get('window').height;

  return (
    <KeyboardAvoidingView
      style={[styles.container, { height: windowHeight * 0.93 }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
    >
      <SafeAreaView style={[styles.modalContainer, { width: windowWidth * 1 }]}>
        {!isWebViewLoaded && (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        )}
        <WebView
          ref={(ref) => {
            webViewRef.current = ref;
          }}
          source={{ html: htmlContent }}
          style={styles.webView}
          javaScriptEnabled
          domStorageEnabled
          originWhitelist={['*']}
          mixedContentMode="compatibility"
          androidLayerType="hardware"
          onMessage={onWebViewMessage}
          keyboardDisplayRequiresUserAction={false}
        />
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    backgroundColor: 'transparent',
    zIndex: 999,
  },
  modalContainer: {
    height: '100%',
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  webView: {
    flex: 1,
    backgroundColor: 'transparent',
  }
});

export default TidioChat;
