import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import Tooltip from 'react-native-walkthrough-tooltip';

type ToolTipContentProps = {
  isVisible: boolean;
  content: string;
  onNext: () => void; // Function to handle moving to the next tooltip
};

const ToolTipContent: React.FC<ToolTipContentProps> = ({
  isVisible,
  content,
  onNext,
}) => {
  const renderTooltipContent = () => (
    <View style={{flexDirection: 'row', alignItems: 'center'}}>
      <Text>{content}</Text>
      <TouchableOpacity
        style={{marginLeft: 10, padding: 5}}
        onPress={onNext} // Handle the next tooltip
      >
        <Text style={{fontWeight: 'bold', color: 'red'}}>X</Text> {/* Close button */}
      </TouchableOpacity>
    </View>
  );

  return (
    <Tooltip
      isVisible={isVisible} // Control visibility
      content={renderTooltipContent()} // Tooltip content
      placement="top"
      showChildInTooltip={false}
    >
      <View />
    </Tooltip>
  );
};

export default ToolTipContent;
