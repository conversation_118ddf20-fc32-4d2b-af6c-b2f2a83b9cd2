import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Platform, StyleSheet, View, TouchableOpacity, Image, Modal, Text, ActivityIndicator, Dimensions } from 'react-native';
import { Camera, CameraDevice } from 'react-native-vision-camera';
import RNFS from 'react-native-fs';
import CameraIcon from '../assets/images/camera-solid.svg';
import CloseIcon from '../assets/images/x-solid.svg';
import TrashIcon from '../assets/images/trash-solid.svg';
import { generateGuid } from './GuidGenerator';

interface CameraAppProps {
  visible: boolean;
  onRequestClose: (imageBase64: string[]) => void;
  images:string[];
}

const CameraApp: React.FC<CameraAppProps> = ({ visible, onRequestClose, images }) => {
  const [imageBase64, setImageBase64s] = useState<string[]>([]);
  const [device, setDevice] = useState<CameraDevice>();
  const [isLoading, setLoadingStatus] = useState(true);
  const cameraRef = useRef<Camera | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | any>(false);
  const windowWidth = Dimensions.get('window').width;
  const inputIconSize = windowWidth * 0.05; 

  const getDevices = useCallback(async () => {
    try {
      const deviceList = await Camera.getAvailableCameraDevices();
      if(images)
      {
        setImageBase64s(images);
      }
      // Set device back camera
      setDevice(deviceList[0]);
      setLoadingStatus(false);
    } catch (e) {
      console.error("trouble getting devices:", e);
    }
  }, []);

  useEffect(() => {
    getDevices();
  }, []);

  const requestCameraPermission = async (): Promise<any> => {
    const status = await Camera.requestCameraPermission();
    if(status === "authorized")
    {
        setHasPermission(true);
    }
  };

  useEffect(() => {
    requestCameraPermission();
  }, []);

  const takePicture = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePhoto();
        const base64Photo = await RNFS.readFile(photo.path, 'base64');
        const base64Image = "data:image/png;base64," + base64Photo;
        setImageBase64s(prevUris => [...prevUris, base64Image]);
      } catch (error) {
        console.log('Error while taking picture:', error);
      }
    }
  };

  const deletePhoto = (indexToDelete:number) => {
    const updatedArray = [...imageBase64];
    updatedArray.splice(indexToDelete, 1);
    setImageBase64s(updatedArray);
  }

 /*  if (Platform.OS === 'android' && !hasPermission) {
    return <Text>No Permission</Text>;
  } */

  return (
    <Modal visible={true}>
      <View style={styles.container}>
        {isLoading ? (
        <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#108a00" />
        </View>
        ) : (
          <Camera
            style={styles.preview}
            device={device as CameraDevice}
            ref={cameraRef}
            photo={true}
            isActive={true}
          />
        )}

       <View style={styles.buttonContainer}>
        <View style={styles.childContainer}>
           <TouchableOpacity onPress={takePicture} style={styles.button}>
                <View style={styles.iconContainer}>
                    <CameraIcon width={inputIconSize} height={inputIconSize} fill="#fff" />
                    <Text style={styles.iconPrimaryText}>Camera</Text>        
                </View>
           </TouchableOpacity>
           
        </View>
        <View style={styles.childContainer}>
            <TouchableOpacity onPress={() => onRequestClose(imageBase64)} style={styles.btnSecondary}>
                <View style={styles.iconContainer}>
                    <CloseIcon width={inputIconSize} height={inputIconSize} fill="#767676" />
                    <Text style={styles.iconSecondaryText}>Close</Text>          
                </View>
            </TouchableOpacity>
        </View>
        </View>
        <View style={styles.imagesContainer}>
          {imageBase64.map((uri, index) => (
            <View style={styles.previewPhoto}>
                <Image key={generateGuid()} source={{uri: uri }} style={styles.image} />
                <TouchableOpacity style={styles.button} key={generateGuid()} onPress={() => deletePhoto(index)}>
                    <View style={styles.iconContainer}>
                        <TrashIcon width={inputIconSize} height={inputIconSize} fill="#fff" />        
                    </View>
                </TouchableOpacity>
            </View>
          ))}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor:'#fff'
  },
  loadingContainer: {
    flex:1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  childContainer : {
    width:'50%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  preview: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width:'80%',
    backgroundColor:'#108a00',
    padding:12,
    borderRadius:25,
    height:40,
    marginTop: 10,
    marginBottom:15,
  },
  buttonText: {
    color:'#fff',
    fontWeight:"bold",
    textAlign:"center"
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
    margin: 5,
  },
  previewPhoto:{
    alignItems:'center'
  },
  btnSecondary: {
    marginTop: 10,
    marginBottom:15,
    width:'80%',
    borderColor:'#c5c5c5',
    borderWidth:2,
    padding:12,
    borderRadius:25,
    height:40
  },
  btnSecondaryText : {
    color:'#767676',
    fontWeight:"bold",
    textAlign:"center"
  },
  iconContainer:{
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent:'center',
    marginTop:-2
  },
  iconPrimaryText : {
    color:'#fff',
    fontWeight:"bold",
    textAlign:"center",
    marginLeft:10
  },
  iconSecondaryText : {
    color:'#767676',
    fontWeight:"bold",
    textAlign:"center",
    marginLeft:10
  },
});

export default CameraApp;
